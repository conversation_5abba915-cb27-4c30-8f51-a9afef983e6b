import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'cms.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # MongoDB Configuration
    MONGODB_URI = os.environ.get('MONGODB_URI') or None
    MONGODB_DATABASE = os.environ.get('MONGODB_DATABASE') or 'character_cms'
    MONGODB_COLLECTION = os.environ.get('MONGODB_COLLECTION') or 'characters'
    MONGODB_ENABLED = bool(os.environ.get('MONGODB_URI'))

    # Qdrant Configuration
    QDRANT_URL = os.environ.get('QDRANT_URL') or None
    QDRANT_API_KEY = os.environ.get('QDRANT_API_KEY') or None
    QDRANT_COLLECTION = os.environ.get('QDRANT_COLLECTION') or 'partnr-chat-image-dev'
    QDRANT_ENABLED = bool(os.environ.get('QDRANT_URL'))

    # CDN Configuration for Qdrant Images
    CDN_BASE_URL = os.environ.get('CDN_BASE_URL') or os.environ.get('IMAGE_CDN_URL') or 'https://cdn.mirailabs.co/partnr/ai-generated'

    # File upload settings
    UPLOAD_FOLDER = os.path.join(basedir, 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

    # Pagination settings
    CHARACTERS_PER_PAGE = 12
    IMAGES_PER_PAGE = 20

    # Thumbnail settings
    THUMBNAIL_SIZE = (300, 300)
    SMALL_THUMBNAIL_SIZE = (150, 150)
    
    @staticmethod
    def init_app(app):
        # Create upload directories
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'characters'), exist_ok=True)
        os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'generated'), exist_ok=True)
        os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'thumbnails'), exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
