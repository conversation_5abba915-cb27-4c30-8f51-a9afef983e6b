#!/usr/bin/env python3
"""
Character CMS Application Entry Point
"""

import os
from app import create_app, db
from app.models import User, Character, GeneratedImage, Tag
from flask_migrate import upgrade

def deploy():
    """Run deployment tasks."""
    # Create database tables
    db.create_all()

    # Create default admin user if it doesn't exist
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True
        )
        admin.set_password('admin123')  # Change this in production!
        db.session.add(admin)
        db.session.commit()
        print("Created default admin user (username: admin, password: admin123)")

    # Create some default tags
    default_tags = [
        {'name': 'Fantasy', 'color': '#8B5CF6'},
        {'name': 'Sci-Fi', 'color': '#06B6D4'},
        {'name': 'Anime', 'color': '#F59E0B'},
        {'name': 'Realistic', 'color': '#10B981'},
        {'name': 'Cartoon', 'color': '#EF4444'},
        {'name': 'Portrait', 'color': '#6366F1'},
        {'name': 'Full Body', 'color': '#EC4899'},
        {'name': 'Action', 'color': '#F97316'},
    ]

    for tag_data in default_tags:
        existing_tag = Tag.query.filter_by(name=tag_data['name']).first()
        if not existing_tag:
            tag = Tag(name=tag_data['name'], color=tag_data['color'])
            db.session.add(tag)

    db.session.commit()
    print("Created default tags")

    # Automatic MongoDB sync on startup
    try:
        from app.services import mongodb_service, character_sync_service

        if mongodb_service.is_enabled():
            print("🔄 MongoDB enabled - performing automatic sync on startup...")

            # Test MongoDB connection
            if mongodb_service.test_connection():
                print("✅ MongoDB connection successful")

                # Get MongoDB character count
                mongo_characters = mongodb_service.get_all_characters()
                mongo_count = len(mongo_characters)

                # Get SQLite character count
                sqlite_count = Character.query.count()

                print(f"📊 Characters in MongoDB: {mongo_count}")
                print(f"📊 Characters in SQLite: {sqlite_count}")

                if mongo_count > 0:
                    # Perform automatic sync from MongoDB to SQLite
                    print("🔄 Syncing characters from MongoDB to SQLite...")
                    stats = character_sync_service.sync_from_mongodb(force_update=False)

                    print(f"📊 Automatic MongoDB Sync Results:")
                    print(f"   ✅ Imported: {stats.get('imported', 0)}")
                    print(f"   🔄 Updated: {stats.get('updated', 0)}")
                    print(f"   ⏭️  Skipped: {stats.get('skipped', 0)}")
                    print(f"   ❌ Errors: {stats.get('errors', 0)}")

                    if stats.get('errors', 0) == 0:
                        print("✅ Automatic MongoDB sync completed successfully")
                    else:
                        print("⚠️  Automatic MongoDB sync completed with errors")
                else:
                    print("ℹ️  No characters found in MongoDB to sync")
            else:
                print("❌ MongoDB connection failed - skipping automatic sync")
        else:
            print("ℹ️  MongoDB not enabled - running in SQLite-only mode")

    except Exception as e:
        print(f"⚠️  Error during automatic MongoDB sync: {e}")
        print("🔄 Application will continue with existing SQLite data")

if __name__ == '__main__':
    app = create_app(os.getenv('FLASK_CONFIG') or 'development')
    
    with app.app_context():
        # Run deployment tasks
        deploy()
    
    # Run the application
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5005)),
        debug=app.config.get('DEBUG', False)
    )
