#!/usr/bin/env python3
"""
Force Recreate Database Script
Completely recreates the database with new schema
"""

import os
import sys
import sqlite3

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(__file__))

def force_recreate_database():
    """Force recreate database with new schema"""
    
    # Remove existing database completely
    db_path = 'character_cms.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"🗑️  Removed existing database: {db_path}")
    
    # Create new empty database file
    conn = sqlite3.connect(db_path)
    conn.close()
    print(f"📁 Created new empty database: {db_path}")
    
    # Import after removing database
    from app import create_app, db
    from app.models import User, Character, Tag
    
    app = create_app()
    
    with app.app_context():
        print("🔍 Force recreating database...")
        
        # Drop all tables first
        db.drop_all()
        print("🗑️  Dropped all existing tables")
        
        # Create all tables with new schema
        db.create_all()
        print("📋 Created all tables with new schema")
        
        # Verify table structure
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        
        if 'character' in inspector.get_table_names():
            columns = inspector.get_columns('character')
            print(f"\n✅ Character table columns ({len(columns)}):")
            for col in columns:
                print(f"  - {col['name']}: {col['type']}")
            
            # Check if new columns exist
            column_names = [col['name'] for col in columns]
            new_columns = ['character_id', 'age', 'occupation', 'hair_color', 'bio', 'voice_id']
            missing = [col for col in new_columns if col not in column_names]
            
            if missing:
                print(f"❌ Missing columns: {missing}")
                return False
            else:
                print("✅ All new columns present!")
        
        # Create admin user
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        
        # Create sample tags
        sample_tags_data = [
            ('Anime', '#ff6b6b'),
            ('Realistic', '#4ecdc4'),
            ('Teacher', '#54a0ff'),
            ('Student', '#5f27cd'),
            ('Fantasy', '#45b7d1'),
        ]
        
        for tag_name, tag_color in sample_tags_data:
            tag = Tag(name=tag_name, color=tag_color)
            db.session.add(tag)
        
        db.session.commit()
        print("✅ Admin user and sample tags created")
        
        # Create enhanced sample character
        character = Character(
            character_id='ayumi_enhanced',
            name='Ayumi (Enhanced)',
            age=29,
            occupation='High School Math Teacher',
            country='Osaka, Japan',
            hobbies='Puzzles, Reading',
            hair_color='Jet Black',
            hair_style='Long',
            eye_color='Icy Blue',
            eye_type='Sharp, with long wispy lashes',
            face_detail='Straight sharp nose, thin dark eyebrows, smirking lips, pale porcelain skin',
            body_detail='Tall and slender figure, large breasts',
            skin_color='Pale Porcelain',
            personality_roleplay='Ayumi often presents a cool, confident exterior, hinted at by her frequent smirk. As a math teacher, she values logic and precision, but might possess a subtly mischievous or teasing side beneath her intelligent demeanor.',
            style_roleplay='Communicates with sharp clarity and precision. May use dry wit or a slightly teasing tone, reflecting her confident smirk and analytical mind.',
            nsfw_style='Approaches intimate topics with a blend of cool confidence and analytical curiosity, possibly using sharp wit or playful challenges.',
            timezone=9,
            gender='Female',
            types='Anime',
            prompt_gen_image='best quality, thigh-up shot, beautiful woman, large breast, piercing, icyblue eyes, straight sharp nose, thin dark eyebrows, long wispy lashes, pale porcelain skin, smirking lips, jetblack hair, tall and slender figure',
            prompt_negative_gen_image='extra arms, extra legs, extra fingers, bad anatomy, hair on face, ugly, close up face',
            prompt_gen_scenario_image='classroom, sunset light, chalkboard glow, wood desks, quiet',
            bio='👩🏻‍🏫 Ayumi, 29. Osaka math teacher.🇯🇵 Icy eyes, jet hair, a hint of mischief. 😉 Puzzles, equations, & exploring the unknown are my passions.🖤 Tall, slender, always up for a challenge. 💪 Care to solve for X? 😈📚',
            scenario_information='Solving for X\nThe chalkboard glows under the last rays of the setting sun as you slip into Ayumi\'s quiet classroom. She adjusts her glasses, setting down a piece of white chalk, her warm smile hiding a hint of playful mischief. With a slight tilt of her head, she taps the board, the unsolved equation daring you to stay and figure her out. In this after-hours world, she\'s both your guide and your greatest mystery.',
            voice_id='af_jessica',
            created_by=admin_user.id
        )
        
        # Set match rate roleset
        roleset = ["Japanese Girl", "Teasing", "Busty", "School Uniform", "Dirty Talker", "Dommy Mommy", "Long Legs", "Late Night Chat", "Cosplayer", "Mean Girl"]
        character.set_match_rate_roleset_list(roleset)
        
        # Add tags
        anime_tag = Tag.query.filter_by(name='Anime').first()
        teacher_tag = Tag.query.filter_by(name='Teacher').first()
        if anime_tag:
            character.tags.append(anime_tag)
        if teacher_tag:
            character.tags.append(teacher_tag)
        
        db.session.add(character)
        db.session.commit()
        
        print("✅ Enhanced sample character created")
        
        # Verify the character
        retrieved = Character.query.filter_by(character_id='ayumi_enhanced').first()
        if retrieved:
            print(f"✅ Character verified: {retrieved.name}")
            print(f"   Age: {retrieved.age}")
            print(f"   Occupation: {retrieved.occupation}")
            print(f"   Bio: {retrieved.bio[:50]}...")
            print(f"   Match Rate Roleset: {retrieved.get_match_rate_roleset_list()[:3]}...")
        
        print("\n🎉 Enhanced database created successfully!")
        print("📊 Database Statistics:")
        print(f"   Users: {User.query.count()}")
        print(f"   Characters: {Character.query.count()}")
        print(f"   Tags: {Tag.query.count()}")
        
        return True

if __name__ == "__main__":
    try:
        force_recreate_database()
        print("\n🌐 You can now start the server:")
        print("   python run.py")
        print("🔑 Login with: admin / admin123")
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
