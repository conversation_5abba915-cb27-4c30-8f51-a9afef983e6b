# Character CMS - Content Management System for Characters and Generated Images

A comprehensive web application built with Flask for managing character profiles and their generated images. Perfect for artists, game developers, writers, and AI art enthusiasts who need to organize and manage character-based content.

## Features

### Character Management
- ✅ Create, read, update, and delete character profiles
- ✅ Store character information (name, description, custom attributes)
- ✅ Upload and manage character template/reference images
- ✅ Character categorization with tags
- ✅ Search and filter characters

### Image Management
- ✅ Upload and organize generated character images
- ✅ Link images to their source character templates
- ✅ Image metadata management (dimensions, file size, creation date)
- ✅ Automatic thumbnail generation
- ✅ Bulk upload functionality
- ✅ Search and filter images

### User System
- ✅ User authentication and authorization
- ✅ Admin panel capabilities
- ✅ User-specific content management

### Technical Features
- ✅ Responsive web interface with Bootstrap 5
- ✅ RESTful API endpoints
- ✅ Image optimization and thumbnail generation
- ✅ Pagination for large datasets
- ✅ File upload with validation
- ✅ Database integration with SQLAlchemy
- ✅ Modern UI/UX design

## Technology Stack

- **Backend**: Flask 3.0.0
- **Database**: SQLite (development) / PostgreSQL (production ready)
- **ORM**: SQLAlchemy with Flask-SQLAlchemy
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF with WTForms
- **Image Processing**: Pillow (PIL)
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **Icons**: Bootstrap Icons

## Installation

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Setup Instructions

1. **Clone or download the project**
   ```bash
   cd cms-image
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment (optional)**
   ```bash
   # Create .env file for custom configuration
   echo "SECRET_KEY=your-secret-key-here" > .env
   echo "FLASK_CONFIG=development" >> .env
   ```

4. **Run the application**
   ```bash
   python run.py
   ```

5. **Access the application**
   - Open your browser and go to `http://127.0.0.1:5000`
   - Default admin credentials: `admin` / `admin123`

## Usage

### Getting Started

1. **Register/Login**: Create an account or use the default admin account
2. **Create Characters**: Add character profiles with descriptions and reference images
3. **Upload Images**: Upload generated images and link them to characters
4. **Organize**: Use tags to categorize characters and search functionality to find content

### Default Admin Account
- **Username**: `admin`
- **Password**: `admin123`
- **Note**: Change this password in production!

### API Endpoints

The application provides RESTful API endpoints:

- `GET /api/characters` - List all characters
- `GET /api/characters/<id>` - Get character details
- `GET /api/images` - List all images
- `GET /api/images/<id>` - Get image details
- `GET /api/tags` - List all tags
- `POST /api/tags` - Create new tag (admin only)
- `GET /api/search?q=<query>` - Search characters and images
- `GET /api/stats` - Get dashboard statistics

### File Structure

```
cms-image/
├── app/
│   ├── __init__.py              # Flask app factory
│   ├── models.py                # Database models
│   ├── routes/                  # Route handlers
│   │   ├── main.py             # Main routes (dashboard, search)
│   │   ├── auth.py             # Authentication routes
│   │   ├── characters.py       # Character management
│   │   ├── images.py           # Image management
│   │   └── api.py              # API endpoints
│   ├── templates/              # HTML templates
│   ├── static/                 # CSS, JS, and static files
│   └── utils/                  # Utility functions
├── uploads/                    # File upload directory
├── config.py                   # Application configuration
├── requirements.txt            # Python dependencies
├── run.py                      # Application entry point
└── README.md                   # This file
```

## Configuration

### Environment Variables

- `SECRET_KEY`: Flask secret key for sessions and CSRF protection
- `DATABASE_URL`: Database connection string
- `FLASK_CONFIG`: Configuration mode (development/production)

### Upload Settings

- **Max file size**: 16MB
- **Allowed formats**: PNG, JPG, JPEG, GIF, WebP
- **Thumbnail sizes**: 300x300 (large), 150x150 (small)

## Development

### Adding New Features

1. **Models**: Add new database models in `app/models.py`
2. **Routes**: Create new route handlers in `app/routes/`
3. **Templates**: Add HTML templates in `app/templates/`
4. **API**: Extend API endpoints in `app/routes/api.py`

### Database Migrations

```bash
# Initialize migrations (first time only)
flask db init

# Create migration
flask db migrate -m "Description of changes"

# Apply migration
flask db upgrade
```

## Production Deployment

### Security Considerations

1. **Change default admin password**
2. **Set strong SECRET_KEY**
3. **Use PostgreSQL for production database**
4. **Configure proper file permissions**
5. **Use HTTPS in production**
6. **Set up proper backup strategy**

### Recommended Production Setup

1. **Web Server**: Nginx
2. **WSGI Server**: Gunicorn
3. **Database**: PostgreSQL
4. **File Storage**: Cloud storage (AWS S3, etc.)
5. **Environment**: Docker container

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For questions, issues, or feature requests, please create an issue in the project repository.

## Changelog

### Version 1.0.0
- Initial release
- Character management system
- Image upload and management
- User authentication
- RESTful API
- Responsive web interface
- Search and filtering
- Tag system
- Bulk upload functionality
