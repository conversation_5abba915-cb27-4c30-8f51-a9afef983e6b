#!/usr/bin/env python3
"""
Character Schema Migration Script
Migrates the Character model to support the new MongoDB-style structure
"""

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def create_app():
    """Create Flask app for migration"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///character_cms.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    return app

def migrate_character_schema():
    """Add new columns to the Character table"""
    app = create_app()
    db = SQLAlchemy(app)
    
    with app.app_context():
        # List of new columns to add
        new_columns = [
            # Basic Information
            ('character_id', 'VARCHAR(100)'),
            ('age', 'INTEGER'),
            ('occupation', 'VARCHAR(200)'),
            ('country', 'VARCHAR(200)'),
            ('hobbies', 'VARCHAR(500)'),
            
            # Physical Appearance
            ('hair_color', 'VARCHAR(100)'),
            ('hair_style', 'VARCHAR(100)'),
            ('eye_color', 'VARCHAR(100)'),
            ('eye_type', 'VARCHAR(200)'),
            ('face_detail', 'TEXT'),
            ('body_detail', 'TEXT'),
            ('skin_color', 'VARCHAR(100)'),
            
            # Personality & Roleplay
            ('personality_roleplay', 'TEXT'),
            ('style_roleplay', 'TEXT'),
            ('nsfw_style', 'TEXT'),
            
            # System Information
            ('timezone', 'INTEGER DEFAULT 0'),
            ('gender', 'VARCHAR(50)'),
            ('types', 'VARCHAR(100)'),
            
            # AI Generation Prompts
            ('prompt_gen_image', 'TEXT'),
            ('prompt_negative_gen_image', 'TEXT'),
            ('prompt_gen_scenario_image', 'TEXT'),
            
            # Additional Information
            ('bio', 'TEXT'),
            ('scenario_information', 'TEXT'),
            ('voice_id', 'VARCHAR(100)'),
            ('match_rate_roleset', 'TEXT')
        ]
        
        print("🔄 Starting Character schema migration...")
        
        # Check if table exists
        result = db.session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='character'"))
        if not result.fetchone():
            print("❌ Character table does not exist. Please run the application first to create the initial schema.")
            return False
        
        # Get existing columns
        result = db.session.execute(text("PRAGMA table_info(character)"))
        existing_columns = {row[1] for row in result.fetchall()}
        
        print(f"📋 Found {len(existing_columns)} existing columns in Character table")
        
        # Add new columns
        added_count = 0
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE character ADD COLUMN {column_name} {column_type}"
                    db.session.execute(text(sql))
                    print(f"✅ Added column: {column_name} ({column_type})")
                    added_count += 1
                except Exception as e:
                    print(f"❌ Failed to add column {column_name}: {e}")
            else:
                print(f"⏭️  Column {column_name} already exists")
        
        # Commit changes
        try:
            db.session.commit()
            print(f"🎉 Migration completed successfully! Added {added_count} new columns.")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ Migration failed: {e}")
            return False

def verify_migration():
    """Verify that the migration was successful"""
    app = create_app()
    db = SQLAlchemy(app)
    
    with app.app_context():
        print("\n🔍 Verifying migration...")
        
        # Get all columns
        result = db.session.execute(text("PRAGMA table_info(character)"))
        columns = [row[1] for row in result.fetchall()]
        
        print(f"📊 Character table now has {len(columns)} columns:")
        for i, column in enumerate(columns, 1):
            print(f"   {i:2d}. {column}")
        
        # Check for key new columns
        required_columns = [
            'character_id', 'age', 'occupation', 'hair_color', 'eye_color',
            'personality_roleplay', 'prompt_gen_image', 'bio', 'voice_id'
        ]
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"⚠️  Missing required columns: {', '.join(missing_columns)}")
            return False
        else:
            print("✅ All required columns are present!")
            return True

def create_sample_character():
    """Create a sample character with the new structure"""
    app = create_app()
    db = SQLAlchemy(app)
    
    with app.app_context():
        print("\n🎭 Creating sample character...")
        
        # Check if we have any users
        result = db.session.execute(text("SELECT id FROM user LIMIT 1"))
        user_row = result.fetchone()
        
        if not user_row:
            print("❌ No users found. Please create a user first.")
            return False
        
        user_id = user_row[0]
        
        # Sample character data based on the MongoDB object
        sample_data = {
            'character_id': 'ayumi_sample',
            'name': 'Ayumi (Sample)',
            'age': 29,
            'occupation': 'High School Math Teacher',
            'country': 'Osaka, Japan',
            'hobbies': 'Puzzles, Reading',
            'hair_color': 'Jet Black',
            'hair_style': 'Long',
            'eye_color': 'Icy Blue',
            'eye_type': 'Sharp, with long wispy lashes',
            'face_detail': 'Straight sharp nose, thin dark eyebrows, smirking lips, pale porcelain skin',
            'body_detail': 'Tall and slender figure, large breasts',
            'skin_color': 'Pale Porcelain',
            'personality_roleplay': 'Ayumi often presents a cool, confident exterior, hinted at by her frequent smirk. As a math teacher, she values logic and precision, but might possess a subtly mischievous or teasing side beneath her intelligent demeanor.',
            'style_roleplay': 'Communicates with sharp clarity and precision. May use dry wit or a slightly teasing tone, reflecting her confident smirk and analytical mind.',
            'nsfw_style': 'Approaches intimate topics with a blend of cool confidence and analytical curiosity, possibly using sharp wit or playful challenges.',
            'timezone': 9,
            'gender': 'Female',
            'types': 'Anime',
            'prompt_gen_image': 'best quality, thigh-up shot, beautiful woman, large breast, piercing, icyblue eyes, straight sharp nose, thin dark eyebrows, long wispy lashes, pale porcelain skin, smirking lips, jetblack hair, tall and slender figure',
            'prompt_negative_gen_image': 'extra arms, extra legs, extra fingers, bad anatomy, hair on face, ugly, close up face',
            'prompt_gen_scenario_image': 'classroom, sunset light, chalkboard glow, wood desks, quiet',
            'bio': '👩🏻‍🏫 Ayumi, 29. Osaka math teacher.🇯🇵 Icy eyes, jet hair, a hint of mischief. 😉 Puzzles, equations, & exploring the unknown are my passions.🖤 Tall, slender, always up for a challenge. 💪 Care to solve for X? 😈📚',
            'scenario_information': 'Solving for X\nThe chalkboard glows under the last rays of the setting sun as you slip into Ayumi\'s quiet classroom. She adjusts her glasses, setting down a piece of white chalk, her warm smile hiding a hint of playful mischief. With a slight tilt of her head, she taps the board, the unsolved equation daring you to stay and figure her out. In this after-hours world, she\'s both your guide and your greatest mystery.',
            'voice_id': 'af_jessica',
            'match_rate_roleset': '["Japanese Girl", "Teasing", "Busty", "School Uniform", "Dirty Talker", "Dommy Mommy", "Long Legs", "Late Night Chat", "Cosplayer", "Mean Girl"]',
            'created_by': user_id
        }
        
        # Build INSERT query
        columns = ', '.join(sample_data.keys())
        placeholders = ', '.join([f":{key}" for key in sample_data.keys()])
        
        sql = f"INSERT INTO character ({columns}) VALUES ({placeholders})"
        
        try:
            db.session.execute(text(sql), sample_data)
            db.session.commit()
            print("✅ Sample character 'Ayumi (Sample)' created successfully!")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ Failed to create sample character: {e}")
            return False

def main():
    """Main migration function"""
    print("🚀 Character CMS - Schema Migration Tool")
    print("=" * 50)
    
    # Step 1: Migrate schema
    if not migrate_character_schema():
        print("❌ Schema migration failed!")
        return 1
    
    # Step 2: Verify migration
    if not verify_migration():
        print("❌ Migration verification failed!")
        return 1
    
    # Step 3: Create sample character
    print("\n" + "=" * 50)
    create_sample = input("🎭 Would you like to create a sample character? (y/N): ").lower().strip()
    
    if create_sample in ['y', 'yes']:
        if not create_sample_character():
            print("⚠️  Sample character creation failed, but migration was successful.")
    
    print("\n🎉 Migration completed successfully!")
    print("📝 You can now use the enhanced character creation form with all the new fields.")
    print("🌐 Access the application at: http://127.0.0.1:5000")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
