# Character CMS Environment Configuration
# Copy this file to .env and configure your settings

# Flask Configuration
SECRET_KEY=your-secret-key-change-in-production
FLASK_CONFIG=development
DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///cms.db

# MongoDB Configuration (Optional)
# Uncomment and configure these lines to enable MongoDB integration
# MONGODB_URI=mongodb://localhost:27017/character_cms
# MONGODB_DATABASE=character_cms
# MONGODB_COLLECTION=characters

# MongoDB Atlas Example (Cloud MongoDB)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/character_cms?retryWrites=true&w=majority

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Pagination Settings
CHARACTERS_PER_PAGE=12
IMAGES_PER_PAGE=20

# Thumbnail Settings
THUMBNAIL_SIZE=300,300
SMALL_THUMBNAIL_SIZE=150,150
