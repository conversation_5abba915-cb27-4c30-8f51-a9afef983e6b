from llama_index.embeddings.vertex import VertexTextEmbedding
from google.oauth2 import service_account
from llama_index.core import VectorStoreIndex
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from llama_index.core.schema import TextNode

from typing import List, Dict
import datetime
from loguru import logger
import os
from dotenv import load_dotenv
import uuid

load_dotenv()


class LLMVertexEmbedding:
    def __init__(self, json_config: str, model_name: str = "text-embedding-large-exp-03-07",
                 location: str = "us-central1"):
        self.json_config = json_config
        self.model_name = model_name
        self.location = location
        self.credentials = None
        self.embedding = None
        self.init()

    def init(self):
        self.credentials = service_account.Credentials.from_service_account_file(self.json_config)
        self.embedding = VertexTextEmbedding(
            model_name=self.model_name,
            project=self.credentials.project_id,
            location=self.location,
            credentials=self.credentials,
        )

    def get_embedding_model(self):
        return self.embedding

    async def get_embedding_text(self, content: str) -> List:
        return await self.embedding.aget_text_embedding(content)


class LlamaIndexQdrant:
    def __init__(self, url_qdrant: str, collection_qdrant: str, top_k=20):
        self.client = QdrantClient(url_qdrant)
        self.collection_name = collection_qdrant
        self.vector_store = QdrantVectorStore(client=self.client, collection_name=self.collection_name)
        self.top_k = top_k
        self.embedding_model = None
        self.index = None
        self.init()

    def init(self):
        self.embedding_model = LLMVertexEmbedding(os.environ["JSON_CONFIG_CREDENTIALS"])
        self.index = VectorStoreIndex.from_vector_store(vector_store=self.vector_store,
                                                        embed_model=self.embedding_model.get_embedding_model())

    async def test_embedding(self):
        matrix = await self.embedding_model.get_embedding_text("hallo")
        print(matrix)

    async def insert(self, content: str, metadata: Dict):
        logger.info("Start inserting one content to qdrant")
        nodes = []
        node_id = str(uuid.uuid4())
        node = TextNode(
            text=content,
            id_=node_id,
            metadata=metadata
        )
        nodes.append(node)
        texts = [node.text for node in nodes]
        embeddings = self.embedding_model.get_text_embedding_batch(texts)
        points = []
        for i, node in enumerate(nodes):
            points.append({
                "id": node.id_,
                "vector": embeddings[i],
                "payload": {
                    "text": node.text,
                    "metadata": node.metadata,
                    "timestamp": datetime.datetime.now().strftime("%d-%m-%Y  %H:%M:%S")
                }
            })
        logger.info("Create node successfully")
        # Bulk insert using Qdrant client directly
        self.client.upsert(
            collection_name=self.collection_name,
            points=points,
            wait=True
        )
        logger.info("Insert one point to qdrant successfully")


if __name__ == '__main__':
    import asyncio

    qdrant = LlamaIndexQdrant("http://35.240.147.237:6333/", "partnr-chat-dev")
    asyncio.run(qdrant.test_embedding())
