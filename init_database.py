#!/usr/bin/env python3
"""
Database Initialization Script
Creates the database tables and initial data for Character CMS
"""

import os
import sys
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(__file__))

from app import create_app, db
from app.models import User, Character, Tag, GeneratedImage

def init_database():
    """Initialize the database with tables and sample data"""
    app = create_app()
    
    with app.app_context():
        print("🗄️  Initializing Character CMS Database...")
        
        # Create all tables
        print("📋 Creating database tables...")
        db.create_all()
        print("✅ Database tables created successfully!")
        
        # Check if admin user exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("👤 Creating admin user...")
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            print("✅ Admin user created (username: admin, password: admin123)")
        else:
            print("👤 Admin user already exists")
        
        # Create sample tags
        sample_tags = [
            ('Anime', '#ff6b6b'),
            ('Realistic', '#4ecdc4'),
            ('Fantasy', '#45b7d1'),
            ('Sci-Fi', '#96ceb4'),
            ('Historical', '#feca57'),
            ('Modern', '#ff9ff3'),
            ('Teacher', '#54a0ff'),
            ('Student', '#5f27cd'),
            ('Warrior', '#00d2d3'),
            ('Mage', '#ff9f43')
        ]
        
        existing_tags = {tag.name for tag in Tag.query.all()}
        added_tags = 0
        
        for tag_name, tag_color in sample_tags:
            if tag_name not in existing_tags:
                tag = Tag(name=tag_name, color=tag_color)
                db.session.add(tag)
                added_tags += 1
        
        if added_tags > 0:
            print(f"🏷️  Created {added_tags} sample tags")
        else:
            print("🏷️  Sample tags already exist")
        
        # Commit changes
        db.session.commit()
        print("💾 Database initialization completed!")
        
        return True

def create_sample_character():
    """Create a sample character with the new structure"""
    app = create_app()
    
    with app.app_context():
        print("\n🎭 Creating sample character...")
        
        # Get admin user
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("❌ Admin user not found!")
            return False
        
        # Check if sample character already exists
        existing_char = Character.query.filter_by(character_id='ayumi_sample').first()
        if existing_char:
            print("🎭 Sample character already exists")
            return True
        
        # Get some tags
        anime_tag = Tag.query.filter_by(name='Anime').first()
        teacher_tag = Tag.query.filter_by(name='Teacher').first()
        
        # Create sample character
        character = Character(
            character_id='ayumi_sample',
            name='Ayumi (Sample)',
            age=29,
            occupation='High School Math Teacher',
            country='Osaka, Japan',
            hobbies='Puzzles, Reading',
            hair_color='Jet Black',
            hair_style='Long',
            eye_color='Icy Blue',
            eye_type='Sharp, with long wispy lashes',
            face_detail='Straight sharp nose, thin dark eyebrows, smirking lips, pale porcelain skin',
            body_detail='Tall and slender figure, large breasts',
            skin_color='Pale Porcelain',
            personality_roleplay='Ayumi often presents a cool, confident exterior, hinted at by her frequent smirk. As a math teacher, she values logic and precision, but might possess a subtly mischievous or teasing side beneath her intelligent demeanor.',
            style_roleplay='Communicates with sharp clarity and precision. May use dry wit or a slightly teasing tone, reflecting her confident smirk and analytical mind.',
            nsfw_style='Approaches intimate topics with a blend of cool confidence and analytical curiosity, possibly using sharp wit or playful challenges.',
            timezone=9,
            gender='Female',
            types='Anime',
            prompt_gen_image='best quality, thigh-up shot, beautiful woman, large breast, piercing, icyblue eyes, straight sharp nose, thin dark eyebrows, long wispy lashes, pale porcelain skin, smirking lips, jetblack hair, tall and slender figure',
            prompt_negative_gen_image='extra arms, extra legs, extra fingers, bad anatomy, hair on face, ugly, close up face',
            prompt_gen_scenario_image='classroom, sunset light, chalkboard glow, wood desks, quiet',
            bio='👩🏻‍🏫 Ayumi, 29. Osaka math teacher.🇯🇵 Icy eyes, jet hair, a hint of mischief. 😉 Puzzles, equations, & exploring the unknown are my passions.🖤 Tall, slender, always up for a challenge. 💪 Care to solve for X? 😈📚',
            scenario_information='Solving for X\nThe chalkboard glows under the last rays of the setting sun as you slip into Ayumi\'s quiet classroom. She adjusts her glasses, setting down a piece of white chalk, her warm smile hiding a hint of playful mischief. With a slight tilt of her head, she taps the board, the unsolved equation daring you to stay and figure her out. In this after-hours world, she\'s both your guide and your greatest mystery.',
            voice_id='af_jessica',
            created_by=admin_user.id
        )
        
        # Set match rate roleset
        roleset = ["Japanese Girl", "Teasing", "Busty", "School Uniform", "Dirty Talker", "Dommy Mommy", "Long Legs", "Late Night Chat", "Cosplayer", "Mean Girl"]
        character.set_match_rate_roleset_list(roleset)
        
        # Add tags
        if anime_tag:
            character.tags.append(anime_tag)
        if teacher_tag:
            character.tags.append(teacher_tag)
        
        db.session.add(character)
        db.session.commit()
        
        print("✅ Sample character 'Ayumi (Sample)' created successfully!")
        return True

def show_database_info():
    """Show information about the database"""
    app = create_app()
    
    with app.app_context():
        print("\n📊 Database Information:")
        print("=" * 30)
        
        user_count = User.query.count()
        character_count = Character.query.count()
        tag_count = Tag.query.count()
        image_count = GeneratedImage.query.count()
        
        print(f"👥 Users: {user_count}")
        print(f"🎭 Characters: {character_count}")
        print(f"🏷️  Tags: {tag_count}")
        print(f"🖼️  Images: {image_count}")
        
        if character_count > 0:
            print("\n🎭 Characters:")
            characters = Character.query.all()
            for char in characters:
                print(f"   • {char.name} (ID: {char.character_id or 'N/A'})")

def main():
    """Main initialization function"""
    print("🚀 Character CMS - Database Initialization")
    print("=" * 50)
    
    # Check if database file exists
    db_path = os.path.join(os.path.dirname(__file__), 'character_cms.db')
    if os.path.exists(db_path):
        print(f"📁 Database file found: {db_path}")
        overwrite = input("⚠️  Database already exists. Overwrite? (y/N): ").lower().strip()
        if overwrite not in ['y', 'yes']:
            print("🔄 Skipping database creation...")
        else:
            os.remove(db_path)
            print("🗑️  Existing database removed")
    
    # Initialize database
    if not init_database():
        print("❌ Database initialization failed!")
        return 1
    
    # Create sample character
    create_sample = input("\n🎭 Create sample character? (Y/n): ").lower().strip()
    if create_sample not in ['n', 'no']:
        create_sample_character()
    
    # Show database info
    show_database_info()
    
    print("\n🎉 Database initialization completed!")
    print("📝 You can now run the application:")
    print("   python run.py")
    print("🌐 Then access: http://127.0.0.1:5000")
    print("👤 Login with: admin / admin123")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
