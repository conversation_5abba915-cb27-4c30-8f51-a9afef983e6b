#!/usr/bin/env python3
"""
Deployment script for Character CMS
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return None

def check_requirements():
    """Check if Python and pip are available"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro} found")
    
    # Check pip
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], check=True, capture_output=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    command = f"{sys.executable} -m pip install -r requirements.txt"
    return run_command(command, "Installing dependencies") is not None

def setup_database():
    """Set up the database"""
    print("🔄 Setting up database...")
    
    # Create uploads directory
    uploads_dir = Path("uploads")
    uploads_dir.mkdir(exist_ok=True)
    (uploads_dir / "characters").mkdir(exist_ok=True)
    (uploads_dir / "generated").mkdir(exist_ok=True)
    (uploads_dir / "thumbnails").mkdir(exist_ok=True)
    
    print("✅ Upload directories created")
    return True

def create_env_file():
    """Create .env file if it doesn't exist"""
    env_file = Path(".env")
    if not env_file.exists():
        print("🔄 Creating .env file...")
        with open(env_file, "w") as f:
            f.write("SECRET_KEY=change-this-in-production\n")
            f.write("FLASK_CONFIG=development\n")
            f.write("DATABASE_URL=sqlite:///cms.db\n")
        print("✅ .env file created")
    else:
        print("✅ .env file already exists")

def main():
    """Main deployment function"""
    print("🚀 Character CMS Deployment Script")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("❌ Database setup failed")
        sys.exit(1)
    
    # Create env file
    create_env_file()
    
    print("\n🎉 Deployment completed successfully!")
    print("\n📋 Next steps:")
    print("1. Run the application: python run.py")
    print("2. Open your browser to: http://127.0.0.1:5000")
    print("3. Login with: admin / admin123")
    print("4. Change the admin password!")
    print("\n⚠️  Remember to:")
    print("- Change the SECRET_KEY in .env for production")
    print("- Change the default admin password")
    print("- Use a proper database (PostgreSQL) for production")

if __name__ == "__main__":
    main()
