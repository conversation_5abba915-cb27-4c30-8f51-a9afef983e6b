#!/usr/bin/env python3
"""
MongoDB Integration Setup Script
Sets up MongoDB integration for Character CMS
"""

import os
import sys
import logging
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(__file__))

from app import create_app, db
from app.models import User, Character, Tag
from app.services import mongodb_service, character_sync_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_mongodb_configuration():
    """Check if MongoDB is properly configured"""
    print("🔍 CHECKING MONGODB CONFIGURATION")
    print("=" * 50)
    
    mongodb_uri = os.environ.get('MONGODB_URI')
    mongodb_database = os.environ.get('MONGODB_DATABASE', 'character_cms')
    mongodb_collection = os.environ.get('MONGODB_COLLECTION', 'characters')
    
    print(f"📋 MongoDB URI: {'✅ Configured' if mongodb_uri else '❌ Not configured'}")
    print(f"📋 Database: {mongodb_database}")
    print(f"📋 Collection: {mongodb_collection}")
    
    if not mongodb_uri:
        print("\n⚠️  MongoDB URI not configured!")
        print("To enable MongoDB integration, set the following environment variables:")
        print("   MONGODB_URI=mongodb://localhost:27017/character_cms")
        print("   MONGODB_DATABASE=character_cms (optional)")
        print("   MONGODB_COLLECTION=characters (optional)")
        print("\nExample .env file:")
        print("   MONGODB_URI=mongodb://localhost:27017/character_cms")
        return False
    
    return True

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("\n🔗 TESTING MONGODB CONNECTION")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        if not mongodb_service.is_enabled():
            print("❌ MongoDB service not enabled")
            return False
        
        try:
            connected = mongodb_service.test_connection()
            if connected:
                print("✅ MongoDB connection successful!")
                
                # Get collection info
                collection = mongodb_service.collection
                if collection:
                    count = collection.count_documents({})
                    print(f"📊 Found {count} documents in collection")
                
                return True
            else:
                print("❌ MongoDB connection failed")
                return False
        except Exception as e:
            print(f"❌ MongoDB connection error: {e}")
            return False

def install_pymongo():
    """Install pymongo if not available"""
    print("\n📦 CHECKING PYMONGO INSTALLATION")
    print("=" * 50)
    
    try:
        import pymongo
        print(f"✅ PyMongo {pymongo.version} is installed")
        return True
    except ImportError:
        print("❌ PyMongo not installed")
        print("Installing PyMongo...")
        
        try:
            import subprocess
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'pymongo==4.6.0'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ PyMongo installed successfully!")
                return True
            else:
                print(f"❌ Failed to install PyMongo: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error installing PyMongo: {e}")
            return False

def sync_existing_characters():
    """Sync existing SQLite characters to MongoDB"""
    print("\n🔄 SYNCING EXISTING CHARACTERS TO MONGODB")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        if not mongodb_service.is_enabled():
            print("❌ MongoDB service not enabled")
            return False
        
        try:
            # Get existing characters
            characters = Character.query.all()
            print(f"📊 Found {len(characters)} characters in SQLite")
            
            if len(characters) == 0:
                print("ℹ️  No characters to sync")
                return True
            
            # Sync to MongoDB
            stats = character_sync_service.sync_to_mongodb()
            
            print(f"📊 Sync Results:")
            print(f"   ✅ Exported: {stats.get('exported', 0)}")
            print(f"   🔄 Updated: {stats.get('updated', 0)}")
            print(f"   ⏭️  Skipped: {stats.get('skipped', 0)}")
            print(f"   ❌ Errors: {stats.get('errors', 0)}")
            
            return stats.get('errors', 0) == 0
            
        except Exception as e:
            print(f"❌ Error syncing characters: {e}")
            return False

def create_sample_mongodb_character():
    """Create a sample character in MongoDB"""
    print("\n🎭 CREATING SAMPLE MONGODB CHARACTER")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        if not mongodb_service.is_enabled():
            print("❌ MongoDB service not enabled")
            return False
        
        try:
            # Check if sample character already exists
            existing = mongodb_service.get_character_by_id('mongodb_sample')
            if existing:
                print("ℹ️  Sample MongoDB character already exists")
                return True
            
            # Create sample character data
            sample_character = {
                "character_id": "mongodb_sample",
                "name": "MongoDB Sample Character",
                "age": 25,
                "occupation": "Database Administrator",
                "country": "San Francisco, USA",
                "hobbies": "Database optimization, Data modeling",
                "hair_color": "Brown",
                "hair_style": "Professional",
                "eye_color": "Blue",
                "eye_type": "Focused and analytical",
                "face_detail": "Professional appearance with glasses, confident expression",
                "body_detail": "Business attire, confident posture",
                "skin_color": "Medium",
                "personality_roleplay": "A methodical database professional who ensures data integrity and optimal performance. Passionate about clean data structures and efficient queries.",
                "style_roleplay": "Communicates with technical precision and enjoys solving complex database problems. Uses database metaphors in conversation.",
                "nsfw_style": "Approaches topics with professional discretion and technical curiosity.",
                "timezone": -8,
                "gender": "Non-binary",
                "types": "Professional",
                "prompt_gen_image": "professional database administrator, glasses, business attire, confident, modern office environment, multiple monitors",
                "prompt_negative_gen_image": "unprofessional, messy, distracted, outdated technology",
                "match_rate_roleset": ["Professional", "Technical", "Database", "Problem-solver", "Methodical", "Analytical", "Efficient", "Reliable"],
                "bio": "💾 MongoDB Sample, 25. SF-based DBA specializing in NoSQL databases. Ensuring data integrity and optimal performance across distributed systems. Always ready to optimize your queries! 🔧📊",
                "scenario_information": "In a modern tech office surrounded by multiple monitors displaying database schemas, query performance metrics, and real-time data flows. The gentle hum of servers provides a comforting background as complex data relationships are visualized on screen.",
                "prompt_gen_scenario_image": "modern tech office, multiple monitors, database schemas, server room, professional lighting, data visualization",
                "voice_id": "professional_tech_voice"
            }
            
            # Insert into MongoDB
            result = mongodb_service.insert_character(sample_character)
            
            if result:
                print("✅ Sample MongoDB character created successfully!")
                print(f"   Character ID: {sample_character['character_id']}")
                print(f"   Name: {sample_character['name']}")
                return True
            else:
                print("❌ Failed to create sample MongoDB character")
                return False
                
        except Exception as e:
            print(f"❌ Error creating sample character: {e}")
            return False

def verify_integration():
    """Verify MongoDB integration is working"""
    print("\n✅ VERIFYING MONGODB INTEGRATION")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        try:
            # Test MongoDB service
            if not mongodb_service.is_enabled():
                print("❌ MongoDB service not enabled")
                return False
            
            # Test connection
            if not mongodb_service.test_connection():
                print("❌ MongoDB connection failed")
                return False
            
            # Test character retrieval
            characters = mongodb_service.get_all_characters()
            print(f"✅ Retrieved {len(characters)} characters from MongoDB")
            
            # Test field mapping
            field_mapping = mongodb_service.get_mongodb_field_mapping()
            print(f"✅ Field mapping configured with {len(field_mapping)} fields")
            
            # Test sync service
            mongodb_fields = mongodb_service.get_mongodb_fields()
            print(f"✅ MongoDB fields configured: {len(mongodb_fields)} fields")
            
            print("\n🎉 MongoDB integration verification successful!")
            return True
            
        except Exception as e:
            print(f"❌ Integration verification failed: {e}")
            return False

def show_usage_instructions():
    """Show usage instructions for MongoDB integration"""
    print("\n📖 MONGODB INTEGRATION USAGE")
    print("=" * 50)
    
    print("🌐 Web Interface:")
    print("   • Access MongoDB sync at: http://localhost:5000/admin/mongodb-sync")
    print("   • Login as admin to access synchronization features")
    print("   • Use the web interface to import/export characters")
    
    print("\n🔧 API Endpoints:")
    print("   • GET  /api/mongodb/status - Check MongoDB status")
    print("   • POST /api/mongodb/sync/from-mongodb - Import from MongoDB")
    print("   • POST /api/mongodb/sync/to-mongodb - Export to MongoDB")
    print("   • GET  /api/mongodb/characters - List MongoDB characters")
    print("   • PUT  /api/mongodb/characters/<id>/field - Update character field")
    
    print("\n⚙️  Environment Variables:")
    print("   • MONGODB_URI - MongoDB connection string (required)")
    print("   • MONGODB_DATABASE - Database name (default: character_cms)")
    print("   • MONGODB_COLLECTION - Collection name (default: characters)")
    
    print("\n🔄 Synchronization:")
    print("   • Characters are automatically synced to MongoDB when created/updated")
    print("   • Use admin interface for bulk import/export operations")
    print("   • Individual field updates sync to both SQLite and MongoDB")

def main():
    """Main setup function"""
    print("🚀 CHARACTER CMS - MONGODB INTEGRATION SETUP")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success_count = 0
    total_steps = 6
    
    # Step 1: Install PyMongo
    if install_pymongo():
        success_count += 1
    
    # Step 2: Check configuration
    if check_mongodb_configuration():
        success_count += 1
        
        # Step 3: Test connection
        if test_mongodb_connection():
            success_count += 1
            
            # Step 4: Sync existing characters
            if sync_existing_characters():
                success_count += 1
            
            # Step 5: Create sample character
            if create_sample_mongodb_character():
                success_count += 1
            
            # Step 6: Verify integration
            if verify_integration():
                success_count += 1
    
    print("\n" + "=" * 70)
    print(f"📊 SETUP RESULTS: {success_count}/{total_steps} steps completed")
    
    if success_count == total_steps:
        print("🎉 MONGODB INTEGRATION SETUP COMPLETED SUCCESSFULLY!")
        print("✅ All components configured and working")
        show_usage_instructions()
        return 0
    else:
        print("⚠️  MONGODB INTEGRATION SETUP INCOMPLETE")
        print("❌ Some steps failed - review the output above")
        
        if success_count >= 2:  # Configuration and connection work
            print("\n💡 Partial Success:")
            print("   • MongoDB is configured and accessible")
            print("   • You can still use the integration features")
            print("   • Some advanced features may not work properly")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
