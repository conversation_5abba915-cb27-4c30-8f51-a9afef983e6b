# MongoDB Integration Guide for Character CMS

## 🎯 **Overview**

The Character CMS now supports MongoDB integration for automatic character data synchronization. This allows you to:

- **Import characters** from MongoDB into the SQLite database
- **Export characters** from SQLite to MongoDB
- **Bidirectional sync** between both databases
- **Individual field updates** that sync to both databases
- **Dynamic UI** that adapts based on MongoDB schema

---

## 🔧 **Setup Instructions**

### **1. Install Dependencies**

The MongoDB integration requires PyMongo:

```bash
pip install pymongo==4.6.0
```

Or install from requirements.txt:

```bash
pip install -r requirements.txt
```

### **2. Configure Environment Variables**

Create a `.env` file in your project root with MongoDB configuration:

```env
# Required: MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/character_cms

# Optional: Database and collection names
MONGODB_DATABASE=character_cms
MONGODB_COLLECTION=characters
```

#### **MongoDB Connection Examples:**

**Local MongoDB:**
```env
MONGODB_URI=mongodb://localhost:27017/character_cms
```

**MongoDB Atlas (Cloud):**
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/character_cms?retryWrites=true&w=majority
```

**MongoDB with Authentication:**
```env
MONGODB_URI=*********************************************************
```

### **3. Run Setup Script**

Execute the setup script to configure and test the integration:

```bash
python setup_mongodb_integration.py
```

This script will:
- ✅ Install PyMongo if needed
- ✅ Test MongoDB connection
- ✅ Sync existing characters to MongoDB
- ✅ Create sample MongoDB character
- ✅ Verify integration is working

---

## 🌐 **Web Interface**

### **Admin Dashboard**

Access the MongoDB synchronization interface at:
```
http://localhost:5000/admin/mongodb-sync
```

**Features:**
- **MongoDB Status**: View connection status and configuration
- **Import from MongoDB**: Sync characters from MongoDB to SQLite
- **Export to MongoDB**: Sync characters from SQLite to MongoDB
- **Character Preview**: View characters in MongoDB
- **Sync Results**: Detailed statistics for sync operations

### **Dynamic Forms**

When MongoDB is enabled, character creation and editing forms automatically adapt to show only fields present in the MongoDB schema:

- **Tabbed Interface**: Organized into 5 categories
- **Field Validation**: Based on MongoDB field types
- **Auto-sync**: New characters automatically sync to MongoDB

---

## 🔌 **API Endpoints**

### **MongoDB Status**
```http
GET /api/mongodb/status
```

**Response:**
```json
{
  "success": true,
  "status": {
    "enabled": true,
    "connected": true,
    "database": "character_cms",
    "collection": "characters"
  }
}
```

### **Import from MongoDB**
```http
POST /api/mongodb/sync/from-mongodb
Content-Type: application/json

{
  "force_update": false
}
```

### **Export to MongoDB**
```http
POST /api/mongodb/sync/to-mongodb
Content-Type: application/json

{
  "character_ids": [1, 2, 3]  // Optional: specific character IDs
}
```

### **List MongoDB Characters**
```http
GET /api/mongodb/characters
```

### **Update Character Field**
```http
PUT /api/mongodb/characters/{character_id}/field
Content-Type: application/json

{
  "field_name": "age",
  "field_value": 30
}
```

---

## 📊 **MongoDB Character Schema**

The integration supports the complete MongoDB character object structure:

```json
{
  "_id": {"$oid": "..."},
  "character_id": "ayumi",
  "name": "Ayumi",
  "age": 29,
  "occupation": "High School Math Teacher",
  "country": "Osaka, Japan",
  "hobbies": "Puzzles, Reading",
  "hair_color": "Jet Black",
  "hair_style": "Long",
  "eye_color": "Icy Blue",
  "eye_type": "Sharp, with long wispy lashes",
  "face_detail": "Straight sharp nose, thin dark eyebrows...",
  "body_detail": "Tall and slender figure, large breasts",
  "skin_color": "Pale Porcelain",
  "personality_roleplay": "Ayumi often presents a cool...",
  "style_roleplay": "Communicates with sharp clarity...",
  "nsfw_style": "Approaches intimate topics with...",
  "timezone": 9,
  "gender": "Female",
  "types": "Anime",
  "prompt_gen_image": "best quality, thigh-up shot...",
  "prompt_negative_gen_image": "extra arms, extra legs...",
  "match_rate_roleset": ["Japanese Girl", "Teasing", "Busty"],
  "bio": "👩🏻‍🏫 Ayumi, 29. Osaka math teacher...",
  "scenario_information": "Solving for X\nThe chalkboard glows...",
  "prompt_gen_scenario_image": "classroom, sunset light...",
  "voice_id": "af_jessica"
}
```

---

## 🔄 **Synchronization Behavior**

### **Automatic Sync**

- **Character Creation**: New characters automatically sync to MongoDB
- **Character Updates**: Changes sync to both SQLite and MongoDB
- **Field Updates**: Individual field changes sync bidirectionally

### **Manual Sync**

- **Bulk Import**: Import all characters from MongoDB
- **Bulk Export**: Export all characters to MongoDB
- **Selective Sync**: Sync specific characters by ID

### **Conflict Resolution**

- **Import**: Existing characters skipped unless `force_update=true`
- **Export**: MongoDB characters updated if they exist
- **Field Updates**: Last write wins (no conflict detection)

---

## 🛡️ **Error Handling**

### **Graceful Degradation**

- **MongoDB Unavailable**: Application continues with SQLite-only mode
- **Connection Failures**: Logged but don't break application functionality
- **Sync Errors**: Detailed error reporting with rollback support

### **Fallback Behavior**

- **Forms**: Show all fields when MongoDB is disabled
- **API**: Returns appropriate error messages
- **UI**: Indicates MongoDB status and availability

---

## 🔧 **Configuration Options**

### **Environment Variables**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `MONGODB_URI` | Yes | None | MongoDB connection string |
| `MONGODB_DATABASE` | No | `character_cms` | Database name |
| `MONGODB_COLLECTION` | No | `characters` | Collection name |

### **Application Settings**

```python
# In config.py
MONGODB_ENABLED = bool(os.environ.get('MONGODB_URI'))
```

---

## 🧪 **Testing**

### **Test MongoDB Connection**
```bash
python -c "
from app import create_app
from app.services import mongodb_service
app = create_app()
with app.app_context():
    print('Connected:', mongodb_service.test_connection())
"
```

### **Test Character Sync**
```bash
python -c "
from app import create_app
from app.services import character_sync_service
app = create_app()
with app.app_context():
    stats = character_sync_service.sync_from_mongodb()
    print('Sync stats:', stats)
"
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

**1. PyMongo Not Found**
```bash
pip install pymongo==4.6.0
```

**2. Connection Timeout**
- Check MongoDB server is running
- Verify connection string format
- Check firewall settings

**3. Authentication Failed**
- Verify username/password in connection string
- Check database permissions

**4. Schema Mismatch**
- Run setup script to verify field mapping
- Check MongoDB documents have expected fields

### **Debug Mode**

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

## 📈 **Performance Considerations**

### **Optimization Tips**

- **Batch Operations**: Use bulk sync for large datasets
- **Selective Sync**: Sync only changed characters
- **Connection Pooling**: MongoDB client handles connection pooling
- **Indexing**: Create indexes on `character_id` field in MongoDB

### **Monitoring**

- **Sync Statistics**: Track import/export success rates
- **Error Logging**: Monitor failed sync operations
- **Performance Metrics**: Measure sync operation times

---

## 🔮 **Future Enhancements**

- **Real-time Sync**: WebSocket-based live synchronization
- **Conflict Resolution**: Advanced merge strategies
- **Schema Validation**: Automatic schema validation
- **Backup Integration**: Automated backup to MongoDB
- **Multi-tenant Support**: Multiple MongoDB databases

---

## 📞 **Support**

For issues with MongoDB integration:

1. **Check Logs**: Review application logs for error details
2. **Run Setup Script**: Use `setup_mongodb_integration.py` for diagnostics
3. **Test Connection**: Verify MongoDB connectivity
4. **Check Documentation**: Review this guide for configuration details

**Note**: The application will continue to work with SQLite-only mode if MongoDB is not configured or unavailable.
