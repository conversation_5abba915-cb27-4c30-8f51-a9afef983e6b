// Main JavaScript for Character CMS

// Loading overlay functions - defined immediately for global availability
window.showLoadingOverlay = function(message = '') {
    // Ensure DOM is ready before manipulating it
    if (document.readyState === 'loading') {
        console.warn('showLoadingOverlay called before DOM ready, deferring...');
        return new Promise((resolve) => {
            document.addEventListener('DOMContentLoaded', () => {
                resolve(window.showLoadingOverlay(message));
            });
        });
    }

    // Remove any existing overlay first
    const existingOverlay = document.querySelector('.loading-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.setAttribute('role', 'dialog');
    overlay.setAttribute('aria-label', message || 'Loading');
    overlay.setAttribute('aria-live', 'polite');

    let content = '<div class="loading-spinner" aria-hidden="true"></div>';
    if (message) {
        content += `<div class="mt-3 text-center"><strong>${message}</strong></div>`;
    }
    overlay.innerHTML = content;

    overlay.style.opacity = '0';
    document.body.appendChild(overlay);

    // Fade in animation
    setTimeout(() => {
        overlay.style.opacity = '1';
    }, 10);

    return overlay;
};

window.hideLoadingOverlay = function(overlay) {
    if (overlay && overlay.parentNode) {
        overlay.style.opacity = '0';
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    } else {
        // Fallback: remove any loading overlay if no specific overlay provided
        const existingOverlay = document.querySelector('.loading-overlay');
        if (existingOverlay) {
            existingOverlay.style.opacity = '0';
            setTimeout(() => {
                if (existingOverlay.parentNode) {
                    existingOverlay.parentNode.removeChild(existingOverlay);
                }
            }, 300);
        }
    }
};

// Make functions available in CMS namespace immediately
window.CMS = window.CMS || {};
window.CMS.showLoadingOverlay = window.showLoadingOverlay;
window.CMS.hideLoadingOverlay = window.hideLoadingOverlay;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Enhanced auto-hide alerts with animation
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            alert.style.transition = 'all 0.5s ease-out';
            alert.style.transform = 'translateX(100%)';
            alert.style.opacity = '0';
            setTimeout(function() {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 500);
        });
    }, 5000);

    // Add fade-in animation to page content
    addPageAnimations();

    // Image upload preview
    setupImagePreview();

    // Drag and drop upload
    setupDragAndDrop();

    // Search functionality
    setupSearch();

    // Bulk operations
    setupBulkOperations();

    // Enhanced card interactions
    setupCardInteractions();

    // Loading states
    setupLoadingStates();
});

// Image upload preview functionality
function setupImagePreview() {
    // Select file inputs that have accept="image/*" but are NOT in upload areas (to avoid conflicts)
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]:not(.upload-area input[type="file"])');

    imageInputs.forEach(function(input) {
        // Skip if this input is inside an upload area (handled separately)
        if (input.closest('.upload-area')) {
            return;
        }

        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Find the correct container for the preview
                    let container = input.parentNode;

                    // Find or create preview element
                    let preview = container.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'image-preview mt-2';
                        container.appendChild(preview);
                    }

                    preview.innerHTML = `
                        <div class="text-center">
                            <img src="${e.target.result}" class="img-thumbnail" style="max-width: 300px; max-height: 300px;">
                            <div class="mt-2">
                                <small class="text-muted">${file.name} (${formatFileSize(file.size)})</small>
                            </div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    });
}

// Drag and drop upload functionality
function setupDragAndDrop() {
    const uploadAreas = document.querySelectorAll('.upload-area');
    
    uploadAreas.forEach(function(area) {
        const fileInput = area.querySelector('input[type="file"]');
        
        if (fileInput) {
            area.addEventListener('click', function() {
                fileInput.click();
            });
            
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                area.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                area.classList.remove('dragover');
            });
            
            area.addEventListener('drop', function(e) {
                e.preventDefault();
                area.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    fileInput.dispatchEvent(new Event('change'));
                }
            });
        }
    });
}

// Search functionality
function setupSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(function(input) {
        let searchTimeout;
        
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performSearch(input.value);
            }, 300);
        });
    });
}

// Perform search with debouncing
function performSearch(query) {
    if (query.length < 2) return;
    
    fetch(`/api/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('Search error:', error);
        });
}

// Display search results
function displaySearchResults(data) {
    const resultsContainer = document.querySelector('.search-results');
    if (!resultsContainer) return;
    
    let html = '';
    
    if (data.characters.length > 0) {
        html += '<div class="search-section"><h6 class="px-3 py-2 mb-0 bg-light">Characters</h6>';
        data.characters.forEach(function(character) {
            html += `
                <div class="search-result-item" onclick="window.location.href='/characters/${character.id}'">
                    <div class="d-flex align-items-center">
                        ${character.thumbnail ? 
                            `<img src="/images/serve/${character.thumbnail}" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">` :
                            '<div class="me-2 bg-light d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 4px;"><i class="bi bi-person"></i></div>'
                        }
                        <div>
                            <div class="fw-bold">${character.name}</div>
                            <small class="text-muted">${character.description || 'No description'}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }
    
    if (data.images.length > 0) {
        html += '<div class="search-section"><h6 class="px-3 py-2 mb-0 bg-light">Images</h6>';
        data.images.forEach(function(image) {
            html += `
                <div class="search-result-item" onclick="window.location.href='/images/${image.id}'">
                    <div class="d-flex align-items-center">
                        <img src="/images/serve/${image.thumbnail}" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                        <div>
                            <div class="fw-bold">${image.character_name}</div>
                            <small class="text-muted">${image.description || image.filename}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }
    
    if (html === '') {
        html = '<div class="px-3 py-2 text-muted">No results found</div>';
    }
    
    resultsContainer.innerHTML = html;
    resultsContainer.style.display = 'block';
}

// Bulk operations functionality
function setupBulkOperations() {
    const selectAllCheckbox = document.querySelector('#selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllCheckbox.checked;
            });
            toggleBulkActions();
        });
    }
    
    itemCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
            toggleBulkActions();
        });
    });
    
    function toggleBulkActions() {
        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
        if (bulkActions) {
            bulkActions.style.display = checkedCount > 0 ? 'block' : 'none';
        }
    }
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading(element) {
    element.innerHTML = '<span class="spinner"></span> Loading...';
    element.disabled = true;
}

function hideLoading(element, originalText) {
    element.innerHTML = originalText;
    element.disabled = false;
}

// API helper functions
function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
        }
    };
    
    return fetch(url, { ...defaultOptions, ...options })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
}

// Enhanced page animations
function addPageAnimations() {
    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }

    // Add staggered animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });

    // Add animation to statistics cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;
        card.classList.add('slide-in-up');
    });
}

// Enhanced card interactions
function setupCardInteractions() {
    const cards = document.querySelectorAll('.card');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}



// Loading states for buttons and forms
function setupLoadingStates() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                showLoading(submitBtn);
            }
        });
    });
}

// Enhanced API request with loading states
function apiRequestWithLoading(url, options = {}) {
    const overlay = window.showLoadingOverlay();

    return apiRequest(url, options)
        .finally(() => {
            window.hideLoadingOverlay(overlay);
        });
}

// Smooth scroll to element
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// Enhanced notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.5s ease-out;
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after duration
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-out';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 500);
    }, duration);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Export functions for global use
window.CMS = {
    formatFileSize,
    showLoading,
    hideLoading,
    apiRequest,
    apiRequestWithLoading,
    performSearch,
    showNotification,
    smoothScrollTo,
    showLoadingOverlay,
    hideLoadingOverlay
};
