/* Enhanced Character CMS Styles */

:root {
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #6c757d;
    --success-color: #10b981;
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --info-color: #06b6d4;
    --info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --warning-color: #f59e0b;
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --danger-color: #ef4444;
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 0.75rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* General Styles */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    line-height: 1.6;
}

/* Layout Improvements */
.min-vh-100 {
    min-height: 100vh !important;
}

main {
    flex: 1;
}

footer {
    margin-top: auto;
}

.navbar {
    background: var(--primary-gradient) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 0.5rem;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Enhanced Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-4px);
}

.card-img-top {
    transition: var(--transition);
    border-radius: 0;
}

.card:hover .card-img-top {
    transform: scale(1.08);
}

.card-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: rgba(248, 250, 252, 0.8);
    border-top: 1px solid rgba(226, 232, 240, 0.8);
}

/* Enhanced Image Gallery Styles */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.image-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    background: white;
}

.image-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--card-shadow-hover);
}

.image-item img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: var(--transition);
}

.image-item:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: var(--transition);
}

.image-item:hover .image-overlay {
    transform: translateY(0);
}

.image-overlay h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.image-overlay p {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Masonry Grid for Images */
.masonry-grid {
    column-count: 4;
    column-gap: 1.5rem;
    padding: 1rem 0;
}

.masonry-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.masonry-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-shadow-hover);
}

@media (max-width: 1200px) {
    .masonry-grid { column-count: 3; }
}

@media (max-width: 768px) {
    .masonry-grid { column-count: 2; }
    .image-gallery { grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); }
}

@media (max-width: 480px) {
    .masonry-grid { column-count: 1; }
    .image-gallery { grid-template-columns: 1fr; }
}

/* Enhanced Form Styles */
.form-control {
    border-radius: 0.5rem;
    border: 2px solid #e2e8f0;
    transition: var(--transition);
    background-color: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background-color: #ffffff;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Enhanced Button Styles */
.btn {
    border-radius: 0.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    padding: 0.75rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-info {
    background: var(--info-gradient);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

/* Tag Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Enhanced Statistics Cards */
.stats-card {
    border-radius: var(--border-radius);
    padding: 2rem;
    color: white;
    text-align: center;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition);
}

.stats-card:hover::before {
    opacity: 1;
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.02);
}

.stats-card h3 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-card p {
    font-size: 1.2rem;
    margin-bottom: 0;
    opacity: 0.95;
    font-weight: 500;
}

.stats-card .card-footer {
    padding: 0.75rem 1.25rem;
    background: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.stats-card .card-footer a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    width: 100%;
    border-radius: 0.375rem;
}

.stats-card .card-footer a:hover {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.stats-card .card-footer a:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Search Styles */
.search-container {
    position: relative;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Upload Area Styles */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 3rem 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.upload-area .selected-file-info {
    color: var(--success-color);
}

.upload-area .selected-file-info i {
    color: var(--success-color);
}

.image-preview {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

.image-preview img {
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1.25rem;
}

h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
}

.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Responsive Styles */
@media (max-width: 1200px) {
    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }

    .stats-card h3 {
        font-size: 2.5rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .navbar-nav {
        text-align: center;
        padding: 1rem 0;
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }

    /* Footer responsive improvements */
    footer .row {
        text-align: center;
    }

    footer .col-md-6:last-child {
        margin-top: 0.5rem;
    }

    /* Stats card footer improvements */
    .stats-card .card-footer a {
        font-size: 0.9rem;
        padding: 0.75rem 0;
    }
}

@media (max-width: 480px) {
    h1 { font-size: 1.75rem; }

    .stats-card {
        padding: 1.5rem;
    }

    .stats-card h3 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Enhanced Loading Animations */
.spinner {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 3px solid rgba(102, 126, 234, 0.2);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Fade In Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Slide In Animation */
@keyframes slideInUp {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.slide-in-up {
    animation: slideInUp 0.8s ease-out;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #cc9a06);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control {
        background-color: #2d2d2d;
        border-color: #495057;
        color: #ffffff;
    }
    
    .form-control:focus {
        background-color: #2d2d2d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
}
