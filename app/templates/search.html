{% extends "base.html" %}

{% block title %}Search Results - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-search"></i> Search Results
            {% if query %}
            <small class="text-muted">for "{{ query }}"</small>
            {% endif %}
        </h1>
    </div>
</div>

{% if query %}
<!-- Search Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            Found {{ characters|length }} character(s) and {{ images|length }} image(s) matching your search.
        </div>
    </div>
</div>

<!-- Characters Results -->
{% if characters %}
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-3">
            <i class="bi bi-people"></i> Characters ({{ characters|length }})
        </h3>
        <div class="row">
            {% for character in characters %}
            <div class="col-md-6 col-lg-4 col-xl-3 mb-3">
                <div class="card h-100">
                    {% if character.thumbnail %}
                    <img src="{{ url_for('images.serve_image', filename=character.thumbnail) }}" 
                         class="card-img-top" style="height: 150px; object-fit: cover;" 
                         alt="{{ character.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 150px;">
                        <i class="bi bi-person fs-1 text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <div class="card-body">
                        <h6 class="card-title">{{ character.name }}</h6>
                        {% if character.description %}
                        <p class="card-text text-muted small">
                            {{ character.description[:80] }}{% if character.description|length > 80 %}...{% endif %}
                        </p>
                        {% endif %}
                        
                        {% if character.tags %}
                        <div class="mb-2">
                            {% for tag in character.tags[:3] %}
                            <span class="badge me-1" style="background-color: {{ tag.color }}; font-size: 0.7rem;">
                                {{ tag.name }}
                            </span>
                            {% endfor %}
                            {% if character.tags|length > 3 %}
                            <span class="text-muted small">+{{ character.tags|length - 3 }} more</span>
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        <small class="text-muted">
                            <i class="bi bi-images"></i> {{ character.generated_images.count() }} images
                        </small>
                    </div>
                    
                    <div class="card-footer">
                        <a href="{{ url_for('characters.view', id=character.id) }}" 
                           class="btn btn-outline-primary btn-sm w-100">
                            <i class="bi bi-eye"></i> View Character
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Images Results -->
{% if images %}
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-3">
            <i class="bi bi-images"></i> Images ({{ images|length }})
        </h3>
        <div class="row">
            {% for image in images %}
            <div class="col-md-4 col-lg-3 col-xl-2 mb-3">
                <div class="card h-100">
                    <img src="{{ url_for('images.serve_image', filename=image.thumbnail) }}" 
                         class="card-img-top" style="height: 120px; object-fit: cover;" 
                         alt="{{ image.original_filename }}">
                    
                    <div class="card-body p-2">
                        <small class="text-muted d-block">{{ image.character.name }}</small>
                        {% if image.description %}
                        <small class="text-muted">{{ image.description[:30] }}{% if image.description|length > 30 %}...{% endif %}</small>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer p-2">
                        <a href="{{ url_for('images.view', id=image.id) }}" 
                           class="btn btn-outline-success btn-sm w-100">
                            <i class="bi bi-eye"></i> View
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- No Results -->
{% if not characters and not images %}
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="bi bi-search fs-1 text-muted"></i>
            <h3 class="text-muted mt-3">No results found</h3>
            <p class="text-muted">Try different keywords or check your spelling.</p>
            <div class="mt-4">
                <a href="{{ url_for('characters.index') }}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-people"></i> Browse Characters
                </a>
                <a href="{{ url_for('images.index') }}" class="btn btn-outline-success">
                    <i class="bi bi-images"></i> Browse Images
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No Search Query -->
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="bi bi-search fs-1 text-muted"></i>
            <h3 class="text-muted mt-3">Enter a search term</h3>
            <p class="text-muted">Use the search box above to find characters and images.</p>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
