{% extends "base.html" %}

{% block title %}Vector Database Images - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-cloud-arrow-down"></i> Vector Database Images</h1>
            <div>
                <a href="{{ url_for('images.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-images"></i> Local Images
                </a>
                <span class="badge bg-info fs-6">{{ total_images }} total images</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="character" class="form-label">Character</label>
                        <select name="character" id="character" class="form-select">
                            <option value="">All Characters</option>
                            {% for char_id in all_character_ids %}
                            <option value="{{ char_id }}" {% if char_id == current_character %}selected{% endif %}>
                                {{ character_names.get(char_id, char_id) }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    

                    
                    <div class="col-md-2">
                        <label for="nsfw" class="form-label">Content</label>
                        <select name="nsfw" id="nsfw" class="form-select">
                            <option value="">All Content</option>
                            <option value="false" {% if current_nsfw == 'false' %}selected{% endif %}>SFW</option>
                            <option value="true" {% if current_nsfw == 'true' %}selected{% endif %}>NSFW</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-funnel"></i> Filter
                        </button>
                        <a href="{{ url_for('images.qdrant_index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Images Grid -->
{% if images %}
<div class="row">
    {% for image in images %}
    {% set metadata = image.metadata %}
    <div class="col-md-4 col-lg-3 col-xl-2 mb-4">
        <div class="card h-100">
            <div class="position-relative">
                <img src="{{ metadata.image_url }}"
                     class="card-img-top" style="height: 200px; object-fit: cover;"
                     alt="Vector Image"
                     loading="lazy"
                     onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                
                <!-- Fallback for broken images -->
                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                     style="height: 200px; display: none;">
                    <i class="bi bi-image fs-1 text-muted"></i>
                </div>
                
                <!-- Image overlay with badges -->
                <div class="position-absolute top-0 start-0 p-2">
                    {% if metadata.type %}
                    <span class="badge bg-primary bg-opacity-75 me-1">{{ metadata.type }}</span>
                    {% endif %}
                    {% if metadata.type_nsfw %}
                    <span class="badge bg-danger bg-opacity-75">NSFW</span>
                    {% else %}
                    <span class="badge bg-success bg-opacity-75">SFW</span>
                    {% endif %}
                </div>
                
                <!-- Action buttons -->
                <div class="position-absolute top-0 end-0 p-2">
                    <div class="btn-group-vertical" role="group">
                        <button type="button" class="btn btn-sm btn-light bg-opacity-75"
                                onclick="viewImageModal('{{ image.id }}', '{{ metadata.image_url }}', {{ metadata | tojson | safe }})"
                                title="View Details">
                            <i class="bi bi-eye"></i>
                        </button>
                        {% if current_user.is_authenticated %}
                        <button type="button" class="btn btn-sm btn-danger bg-opacity-75" 
                                onclick="confirmDeleteImage('{{ image.id }}', '{{ metadata.character_id }}')"
                                title="Delete Image">
                            <i class="bi bi-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card-body p-2">
                <h6 class="card-title mb-1">
                    {{ character_names.get(metadata.character_id, metadata.character_id) }}
                </h6>
                <small class="text-muted d-block">
                    <i class="bi bi-person"></i> {{ metadata.character_id }}
                </small>
                {% if metadata.timestamp %}
                <small class="text-muted d-block">
                    <i class="bi bi-calendar"></i> {{ metadata.timestamp }}
                </small>
                {% endif %}
                {% if metadata.user_id %}
                <small class="text-muted d-block">
                    <i class="bi bi-user"></i> {{ metadata.user_id }}
                </small>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if pagination.pages > 1 %}
<nav aria-label="Vector images pagination">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.qdrant_index', page=pagination.prev_num, character=current_character, nsfw=current_nsfw) }}">
                Previous
            </a>
        </li>
        {% endif %}
        
        {% set start_page = [pagination.page - 2, 1] | max %}
        {% set end_page = [pagination.page + 2, pagination.pages] | min %}
        
        {% if start_page > 1 %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.qdrant_index', page=1, character=current_character, nsfw=current_nsfw) }}">1</a>
        </li>
        {% if start_page > 2 %}
        <li class="page-item disabled"><span class="page-link">...</span></li>
        {% endif %}
        {% endif %}
        
        {% for page_num in range(start_page, end_page + 1) %}
        {% if page_num == pagination.page %}
        <li class="page-item active">
            <span class="page-link">{{ page_num }}</span>
        </li>
        {% else %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.qdrant_index', page=page_num, character=current_character, nsfw=current_nsfw) }}">
                {{ page_num }}
            </a>
        </li>
        {% endif %}
        {% endfor %}
        
        {% if end_page < pagination.pages %}
        {% if end_page < pagination.pages - 1 %}
        <li class="page-item disabled"><span class="page-link">...</span></li>
        {% endif %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.qdrant_index', page=pagination.pages, character=current_character, nsfw=current_nsfw) }}">{{ pagination.pages }}</a>
        </li>
        {% endif %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.qdrant_index', page=pagination.next_num, character=current_character, nsfw=current_nsfw) }}">
                Next
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="bi bi-cloud-slash fs-1 text-muted"></i>
    <h3 class="text-muted mt-3">No vector images found</h3>
    {% if current_character or current_type or current_nsfw %}
    <p class="text-muted">Try adjusting your filter criteria.</p>
    <a href="{{ url_for('images.qdrant_index') }}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> View All Images
    </a>
    {% else %}
    <p class="text-muted">No images are available in the vector database.</p>
    {% endif %}
</div>
{% endif %}

<!-- Image Detail Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Image Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <img id="modalImage" src="" class="img-fluid rounded" alt="Image">
                    </div>
                    <div class="col-md-6">
                        <div id="modalMetadata"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this image from the vector database?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewImageModal(imageId, imageUrl, metadata) {
    document.getElementById('modalImage').src = imageUrl;
    
    let metadataHtml = '<h6>Metadata</h6>';
    metadataHtml += '<table class="table table-sm">';
    
    for (const [key, value] of Object.entries(metadata)) {
        if (key !== 'image_link') {
            metadataHtml += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
        }
    }
    
    metadataHtml += '</table>';
    document.getElementById('modalMetadata').innerHTML = metadataHtml;
    
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

function confirmDeleteImage(imageId, characterId) {
    document.getElementById('confirmDeleteBtn').onclick = function() {
        deleteImage(imageId, characterId);
    };
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function deleteImage(imageId, characterId) {
    // This would need to be implemented as an API endpoint
    fetch(`/api/qdrant/images/${imageId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error deleting image: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error deleting image: ' + error);
    });
}
</script>
{% endblock %}
