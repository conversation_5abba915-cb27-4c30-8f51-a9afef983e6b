{% extends "base.html" %}

{% block title %}Edit {{ image.original_filename }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-pencil-square"></i> Edit Image</h1>
            <div>
                <a href="{{ url_for('images.view', id=image.id) }}" class="btn btn-outline-secondary">
                    <i class="bi bi-eye"></i> View Image
                </a>
                <a href="{{ url_for('images.index') }}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Images
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Image Details</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.character_id.label(class="form-label") }}
                        {{ form.character_id(class="form-select") }}
                        {% if form.character_id.errors %}
                            <div class="text-danger">
                                {% for error in form.character_id.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3", placeholder="Describe this generated image...") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.generation_parameters.label(class="form-label") }}
                        <small class="text-muted">(Optional JSON format for generation metadata)</small>
                        {{ form.generation_parameters(class="form-control", rows="8") }}
                        {% if form.generation_parameters.errors %}
                            <div class="text-danger">
                                {% for error in form.generation_parameters.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('images.view', id=image.id) }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary", value="Update Image") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-image"></i> Current Image</h6>
            </div>
            <div class="card-body p-0">
                <img src="{{ url_for('images.serve_image', filename=image.thumbnail) }}" 
                     class="img-fluid w-100" 
                     alt="{{ image.original_filename }}"
                     style="max-height: 300px; object-fit: cover;">
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <strong>{{ image.original_filename }}</strong><br>
                    {{ image.width }}×{{ image.height }} px
                    {% if image.file_size %}
                    • {{ "%.1f"|format(image.file_size / 1024 / 1024) }} MB
                    {% endif %}
                </small>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Image Details</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">Uploaded:</dt>
                    <dd class="col-sm-7">{{ image.created_at.strftime('%Y-%m-%d %H:%M') }}</dd>
                    
                    <dt class="col-sm-5">Uploaded by:</dt>
                    <dd class="col-sm-7">{{ image.uploader.username }}</dd>
                    
                    <dt class="col-sm-5">Current Character:</dt>
                    <dd class="col-sm-7">
                        <a href="{{ url_for('characters.view', id=image.character.id) }}" 
                           class="text-decoration-none">
                            {{ image.character.name }}
                        </a>
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightbulb"></i> Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Character:</strong> You can move this image to a different character.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Description:</strong> Add or update the description to help with searching.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Parameters:</strong> Include generation settings for future reference.
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-file-earmark-code"></i> Example Parameters</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <pre><code>{
  "prompt": "fantasy warrior with sword",
  "negative_prompt": "blurry, low quality",
  "model": "stable-diffusion-xl",
  "steps": 30,
  "cfg_scale": 7.5,
  "width": 1024,
  "height": 1024,
  "seed": 12345,
  "sampler": "DPM++ 2M Karras"
}</code></pre>
                </small>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Danger Zone</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">Permanently delete this image.</p>
                <button type="button" class="btn btn-danger btn-sm w-100" 
                        onclick="confirmDelete('{{ image.original_filename }}', '{{ url_for('images.delete', id=image.id) }}')">
                    <i class="bi bi-trash"></i> Delete Image
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the image "<span id="deleteImageName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // JSON validation for generation parameters field
    const parametersField = document.querySelector('#generation_parameters');
    if (parametersField) {
        parametersField.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value && value !== '') {
                try {
                    JSON.parse(value);
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    }
});

function confirmDelete(imageName, deleteUrl) {
    document.getElementById('deleteImageName').textContent = imageName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
