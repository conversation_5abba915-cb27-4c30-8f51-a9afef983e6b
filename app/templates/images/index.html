{% extends "base.html" %}

{% block title %}Generated Images - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-images"></i> Generated Images</h1>
            {% if current_user.is_authenticated %}
            <div>
                <a href="{{ url_for('images.upload') }}" class="btn btn-success me-2">
                    <i class="bi bi-upload"></i> Upload Image
                </a>
                <a href="{{ url_for('images.bulk_upload') }}" class="btn btn-info">
                    <i class="bi bi-cloud-upload"></i> Bulk Upload
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Search images...">
                    </div>
                    <div class="col-md-4">
                        <label for="character" class="form-label">Filter by Character</label>
                        <select class="form-select" id="character" name="character">
                            <option value="">All Characters</option>
                            {% for character in all_characters %}
                            <option value="{{ character.id }}" {% if current_character == character.id %}selected{% endif %}>
                                {{ character.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="bi bi-search"></i> Filter
                        </button>
                        <a href="{{ url_for('images.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Images Grid -->
{% if images %}
<div class="row">
    {% for image in images %}
    <div class="col-md-4 col-lg-3 col-xl-2 mb-4">
        <div class="card h-100">
            <div class="position-relative">
                <img src="{{ url_for('images.serve_image', filename=image.thumbnail) }}" 
                     class="card-img-top" style="height: 200px; object-fit: cover;" 
                     alt="{{ image.original_filename }}">
                
                <!-- Image overlay with info -->
                <div class="position-absolute top-0 end-0 p-2">
                    <span class="badge bg-dark bg-opacity-75">
                        {{ image.width }}x{{ image.height }}
                    </span>
                </div>
            </div>
            
            <div class="card-body p-2">
                <h6 class="card-title mb-1">
                    <a href="{{ url_for('characters.view', id=image.character.id) }}" 
                       class="text-decoration-none">
                        {{ image.character.name }}
                    </a>
                </h6>
                
                {% if image.description %}
                <p class="card-text text-muted small mb-1">
                    {{ image.description[:50] }}{% if image.description|length > 50 %}...{% endif %}
                </p>
                {% endif %}
                
                <small class="text-muted">
                    <i class="bi bi-calendar"></i> {{ image.created_at.strftime('%m/%d/%Y') }}<br>
                    <i class="bi bi-person"></i> {{ image.uploader.username }}<br>
                    {% if image.file_size %}
                    <i class="bi bi-file-earmark"></i> {{ "%.1f"|format(image.file_size / 1024 / 1024) }} MB
                    {% endif %}
                </small>
            </div>
            
            <div class="card-footer p-2">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('images.view', id=image.id) }}" 
                       class="btn btn-outline-success btn-sm">
                        <i class="bi bi-eye"></i> View
                    </a>
                    {% if can_edit_image(image) %}
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('images.edit', id=image.id) }}"
                           class="btn btn-outline-secondary btn-sm"
                           title="Edit image">
                            <i class="bi bi-pencil"></i>
                        </a>
                        {% if can_delete_image(image) %}
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="confirmDelete('{{ image.original_filename }}', '{{ url_for('images.delete', id=image.id) }}')"
                                title="Delete image">
                            <i class="bi bi-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if pagination.pages > 1 %}
<nav aria-label="Images pagination">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.index', page=pagination.prev_num, search=search_query, character=current_character) }}">
                Previous
            </a>
        </li>
        {% endif %}
        
        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('images.index', page=page_num, search=search_query, character=current_character) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('images.index', page=pagination.next_num, search=search_query, character=current_character) }}">
                Next
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="bi bi-images fs-1 text-muted"></i>
    <h3 class="text-muted mt-3">No images found</h3>
    {% if search_query or current_character %}
    <p class="text-muted">Try adjusting your search criteria.</p>
    <a href="{{ url_for('images.index') }}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> View All Images
    </a>
    {% else %}
    <p class="text-muted">Upload some generated images to get started.</p>
    {% if current_user.is_authenticated %}
    <a href="{{ url_for('images.upload') }}" class="btn btn-success">
        <i class="bi bi-upload"></i> Upload Image
    </a>
    {% endif %}
    {% endif %}
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the image "<span id="deleteImageName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(imageName, deleteUrl) {
    document.getElementById('deleteImageName').textContent = imageName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
