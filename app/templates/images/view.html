{% extends "base.html" %}

{% block title %}{{ image.original_filename }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-image"></i> {{ image.original_filename }}</h1>
            <div>
                <a href="{{ url_for('images.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Images
                </a>
                {% if can_edit_image(image) %}
                <a href="{{ url_for('images.edit', id=image.id) }}" class="btn btn-outline-primary">
                    <i class="bi bi-pencil"></i> Edit
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Image Preview</h5>
            </div>
            <div class="card-body p-0">
                <div class="position-relative">
                    <img src="{{ url_for('images.serve_image', filename=image.filename, generated=1) }}" 
                         class="img-fluid w-100" 
                         alt="{{ image.original_filename }}"
                         style="max-height: 70vh; object-fit: contain; background: #f8f9fa;">
                    
                    <!-- Image overlay with metadata -->
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-dark bg-opacity-75 fs-6">
                            {{ image.width }}×{{ image.height }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">
                            <i class="bi bi-download"></i> 
                            <a href="{{ url_for('images.serve_image', filename=image.filename, generated=1) }}" 
                               download="{{ image.original_filename }}" class="text-decoration-none">
                                Download Original
                            </a>
                        </small>
                    </div>
                    <div>
                        {% if image.file_size %}
                        <small class="text-muted">
                            <i class="bi bi-file-earmark"></i> {{ "%.1f"|format(image.file_size / 1024 / 1024) }} MB
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        {% if image.description %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-chat-text"></i> Description</h6>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ image.description }}</p>
            </div>
        </div>
        {% endif %}
        
        {% if image.generation_parameters %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-gear"></i> Generation Parameters</h6>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>{{ image.generation_parameters }}</code></pre>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Image Details</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">Character:</dt>
                    <dd class="col-sm-7">
                        <a href="{{ url_for('characters.view', id=image.character.id) }}" 
                           class="text-decoration-none">
                            {{ image.character.name }}
                        </a>
                    </dd>
                    
                    <dt class="col-sm-5">Filename:</dt>
                    <dd class="col-sm-7"><small>{{ image.filename }}</small></dd>
                    
                    <dt class="col-sm-5">Dimensions:</dt>
                    <dd class="col-sm-7">{{ image.width }}×{{ image.height }} px</dd>
                    
                    {% if image.file_size %}
                    <dt class="col-sm-5">File Size:</dt>
                    <dd class="col-sm-7">{{ "%.1f"|format(image.file_size / 1024 / 1024) }} MB</dd>
                    {% endif %}
                    
                    <dt class="col-sm-5">Uploaded:</dt>
                    <dd class="col-sm-7">{{ image.created_at.strftime('%Y-%m-%d %H:%M') }}</dd>
                    
                    <dt class="col-sm-5">Uploaded by:</dt>
                    <dd class="col-sm-7">{{ image.uploader.username }}</dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-person"></i> Character Info</h6>
            </div>
            <div class="card-body">
                {% if image.character.thumbnail %}
                <div class="text-center mb-3">
                    <img src="{{ url_for('images.serve_image', filename=image.character.thumbnail) }}" 
                         class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;" 
                         alt="{{ image.character.name }}">
                </div>
                {% endif %}
                
                <h6>{{ image.character.name }}</h6>
                {% if image.character.description %}
                <p class="text-muted small">{{ image.character.description[:100] }}{% if image.character.description|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                {% if image.character.tags %}
                <div class="mb-2">
                    {% for tag in image.character.tags %}
                    <span class="badge me-1" style="background-color: {{ tag.color }}; font-size: 0.7rem;">
                        {{ tag.name }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <a href="{{ url_for('characters.view', id=image.character.id) }}" 
                   class="btn btn-outline-primary btn-sm w-100">
                    <i class="bi bi-eye"></i> View Character
                </a>
            </div>
        </div>
        
        {% if current_user.is_authenticated %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <a href="{{ url_for('images.upload') }}?character_id={{ image.character.id }}" 
                   class="btn btn-success btn-sm w-100 mb-2">
                    <i class="bi bi-upload"></i> Upload Another Image
                </a>
                
                {% if can_edit_image(image) %}
                <a href="{{ url_for('images.edit', id=image.id) }}"
                   class="btn btn-outline-primary btn-sm w-100 mb-2">
                    <i class="bi bi-pencil"></i> Edit Details
                </a>
                {% endif %}

                {% if can_delete_image(image) %}
                <button type="button" class="btn btn-outline-danger btn-sm w-100"
                        onclick="confirmDelete('{{ image.original_filename }}', '{{ url_for('images.delete', id=image.id) }}')">
                    <i class="bi bi-trash"></i> Delete Image
                </button>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Related Images -->
        {% if related_images %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-images"></i> More from {{ image.character.name }}</h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    {% for related in related_images %}
                    <div class="col-6">
                        <a href="{{ url_for('images.view', id=related.id) }}" class="text-decoration-none">
                            <img src="{{ url_for('images.serve_image', filename=related.thumbnail) }}" 
                                 class="img-fluid rounded" style="height: 80px; width: 100%; object-fit: cover;" 
                                 alt="{{ related.original_filename }}">
                        </a>
                    </div>
                    {% endfor %}
                </div>
                
                {% if total_images_count > 5 %}
                <div class="text-center mt-2">
                    <a href="{{ url_for('characters.view', id=image.character.id) }}"
                       class="btn btn-outline-primary btn-sm">
                        View All ({{ total_images_count }})
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the image "<span id="deleteImageName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(imageName, deleteUrl) {
    document.getElementById('deleteImageName').textContent = imageName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
