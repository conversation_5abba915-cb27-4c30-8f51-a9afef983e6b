{% extends "base.html" %}

{% block title %}Bulk Upload Images - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-cloud-upload"></i> Bulk Upload Images</h1>
            <div>
                <a href="{{ url_for('images.upload') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-upload"></i> Single Upload
                </a>
                <a href="{{ url_for('images.index') }}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Images
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Upload Multiple Images</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="bulkUploadForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="mb-3">
                        <label for="character_id" class="form-label">Character</label>
                        <select class="form-select" id="character_id" name="character_id" required>
                            <option value="">Select a character...</option>
                            {% for character in characters %}
                            <option value="{{ character.id }}" 
                                    {% if request.args.get('character_id') == character.id|string %}selected{% endif %}>
                                {{ character.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="files" class="form-label">Select Images</label>
                        <div class="upload-area" id="dropZone">
                            <input type="file" class="form-control" id="files" name="files" 
                                   multiple accept="image/*" style="display: none;" required>
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mt-2 mb-0">Click to select multiple images or drag and drop them here</p>
                            <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP (Max 16MB each)</small>
                        </div>
                    </div>
                    
                    <!-- File Preview Area -->
                    <div id="filePreview" class="mb-3" style="display: none;">
                        <h6>Selected Files:</h6>
                        <div id="fileList" class="row g-2"></div>
                    </div>
                    
                    <!-- Upload Progress -->
                    <div id="uploadProgress" class="mb-3" style="display: none;">
                        <h6>Upload Progress:</h6>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-center mt-2">
                            <small class="text-muted">Preparing upload...</small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('images.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success" id="uploadBtn" disabled>
                            <i class="bi bi-cloud-upload"></i> Upload Images
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Bulk Upload Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Multiple Selection:</strong> Hold Ctrl/Cmd to select multiple files at once.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Drag & Drop:</strong> Drag files directly from your file explorer.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>File Limits:</strong> Each file must be under 16MB.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Processing:</strong> Images will be automatically optimized and thumbnails generated.
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-speedometer2"></i> Upload Statistics</h6>
            </div>
            <div class="card-body">
                <div id="uploadStats">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h4 mb-0" id="totalFiles">0</div>
                            <small class="text-muted">Files</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 mb-0" id="totalSize">0 MB</div>
                            <small class="text-muted">Total Size</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 mb-0" id="uploadedFiles">0</div>
                            <small class="text-muted">Uploaded</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-people"></i> Available Characters</h6>
            </div>
            <div class="card-body">
                {% if characters %}
                <div class="list-group list-group-flush">
                    {% for character in characters[:5] %}
                    <div class="list-group-item d-flex align-items-center px-0">
                        {% if character.thumbnail %}
                        <img src="{{ url_for('images.serve_image', filename=character.thumbnail) }}" 
                             class="rounded me-2" style="width: 30px; height: 30px; object-fit: cover;" 
                             alt="{{ character.name }}">
                        {% else %}
                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                             style="width: 30px; height: 30px;">
                            <i class="bi bi-person text-muted"></i>
                        </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ character.name }}</div>
                            <small class="text-muted">{{ character.generated_images.count() }} images</small>
                        </div>
                    </div>
                    {% endfor %}
                    {% if characters|length > 5 %}
                    <div class="list-group-item px-0 text-center">
                        <small class="text-muted">... and {{ characters|length - 5 }} more</small>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No characters available. Create a character first.</p>
                <a href="{{ url_for('characters.create') }}" class="btn btn-primary btn-sm mt-2 w-100">
                    <i class="bi bi-plus-circle"></i> Create Character
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('files');
    const filePreview = document.getElementById('filePreview');
    const fileList = document.getElementById('fileList');
    const uploadBtn = document.getElementById('uploadBtn');
    const form = document.getElementById('bulkUploadForm');
    
    let selectedFiles = [];
    
    // Click to select files
    dropZone.addEventListener('click', function() {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });
    
    // Drag and drop
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });
    
    function handleFiles(files) {
        selectedFiles = Array.from(files);
        updateFilePreview();
        updateStats();
        uploadBtn.disabled = selectedFiles.length === 0;
    }
    
    function updateFilePreview() {
        if (selectedFiles.length === 0) {
            filePreview.style.display = 'none';
            return;
        }
        
        filePreview.style.display = 'block';
        fileList.innerHTML = '';
        
        selectedFiles.forEach((file, index) => {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-4 col-6';
            
            const reader = new FileReader();
            reader.onload = function(e) {
                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}" class="card-img-top" style="height: 100px; object-fit: cover;">
                        <div class="card-body p-2">
                            <small class="text-muted">${file.name}</small><br>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                            <button type="button" class="btn btn-sm btn-outline-danger w-100 mt-1" 
                                    onclick="removeFile(${index})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
            
            fileList.appendChild(col);
        });
    }
    
    function updateStats() {
        const totalFiles = selectedFiles.length;
        const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
        
        document.getElementById('totalFiles').textContent = totalFiles;
        document.getElementById('totalSize').textContent = formatFileSize(totalSize);
    }
    
    window.removeFile = function(index) {
        selectedFiles.splice(index, 1);
        updateFilePreview();
        updateStats();
        uploadBtn.disabled = selectedFiles.length === 0;
        
        // Update file input
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        fileInput.files = dt.files;
    };
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    // Form submission with progress
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const characterId = document.getElementById('character_id').value;
        if (!characterId) {
            alert('Please select a character');
            return;
        }
        
        if (selectedFiles.length === 0) {
            alert('Please select at least one file');
            return;
        }
        
        uploadFiles();
    });
    
    function uploadFiles() {
        const progressContainer = document.getElementById('uploadProgress');
        const progressBar = progressContainer.querySelector('.progress-bar');
        const progressText = document.getElementById('progressText');
        const uploadedCounter = document.getElementById('uploadedFiles');
        
        progressContainer.style.display = 'block';
        uploadBtn.disabled = true;
        
        let uploaded = 0;
        const total = selectedFiles.length;
        
        // Upload files one by one (or implement batch upload)
        uploadFilesSequentially(0, uploaded, total, progressBar, progressText, uploadedCounter);
    }
    
    function uploadFilesSequentially(index, uploaded, total, progressBar, progressText, uploadedCounter) {
        if (index >= total) {
            // All files uploaded
            progressText.innerHTML = '<span class="text-success">All files uploaded successfully!</span>';
            setTimeout(() => {
                window.location.href = "{{ url_for('images.index') }}";
            }, 2000);
            return;
        }
        
        const file = selectedFiles[index];
        const formData = new FormData();
        formData.append('files', file);
        formData.append('character_id', document.getElementById('character_id').value);
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
        
        progressText.innerHTML = `Uploading ${file.name}... (${index + 1}/${total})`;
        
        fetch('{{ url_for("images.bulk_upload") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                uploaded++;
                uploadedCounter.textContent = uploaded;
                const progress = (uploaded / total) * 100;
                progressBar.style.width = progress + '%';
                
                // Upload next file
                uploadFilesSequentially(index + 1, uploaded, total, progressBar, progressText, uploadedCounter);
            } else {
                throw new Error(`Failed to upload ${file.name}`);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            progressText.innerHTML = `<span class="text-danger">Error: ${error.message}</span>`;
            uploadBtn.disabled = false;

            // Hide any loading overlay on error
            try {
                if (typeof window.hideLoadingOverlay === 'function') {
                    window.hideLoadingOverlay();
                }
            } catch (overlayError) {
                console.error('Error hiding loading overlay:', overlayError);
            }
        });
    }
});
</script>
{% endblock %}
