{% extends "base.html" %}

{% block title %}Upload Image - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-upload"></i> Upload Generated Image</h1>
            <a href="{{ url_for('images.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Images
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Image Upload</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.character_id.label(class="form-label") }}
                        {{ form.character_id(class="form-select") }}
                        {% if form.character_id.errors %}
                            <div class="text-danger">
                                {% for error in form.character_id.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.image_file.label(class="form-label") }}
                        <div class="upload-area">
                            {{ form.image_file(class="form-control", style="display: none;", accept="image/*") }}
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mt-2 mb-0">Click to select or drag and drop an image</p>
                            <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP (Max 16MB)</small>
                        </div>
                        {% if form.image_file.errors %}
                            <div class="text-danger mt-2">
                                {% for error in form.image_file.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        <span class="text-danger">*</span>
                        {{ form.description(class="form-control", rows="3", placeholder="Describe this generated image...", required=true) }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="text-muted">Required field - Please provide a detailed description of the image</small>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.generation_parameters.label(class="form-label") }}
                        <small class="text-muted">(Optional JSON format for generation metadata)</small>
                        {{ form.generation_parameters(class="form-control", rows="6", placeholder='{\n  "prompt": "fantasy warrior with sword",\n  "model": "stable-diffusion-xl",\n  "steps": 30,\n  "cfg_scale": 7.5,\n  "seed": 12345\n}') }}
                        {% if form.generation_parameters.errors %}
                            <div class="text-danger">
                                {% for error in form.generation_parameters.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="bi bi-robot"></i> AI Processing Options
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch">
                                    {{ form.scenario_mode(class="form-check-input", id="scenario_mode") }}
                                    {{ form.scenario_mode.label(class="form-check-label", for="scenario_mode") }}
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-info-circle"></i>
                                    Enable Scenario Mode to process this image with AI for enhanced character interactions and vector database storage.
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('images.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Upload Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Character:</strong> Select the character this image was generated for.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Image File:</strong> Upload high-quality generated images for best results.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-exclamation-circle text-danger"></i>
                        <strong>Description:</strong> <span class="text-danger">Required!</span> Add detailed description about the image content or style.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Parameters:</strong> Include generation settings for reference.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-robot text-info"></i>
                        <strong>Scenario Mode:</strong> Enable AI processing for enhanced character interactions.
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <a href="{{ url_for('images.bulk_upload') }}" class="btn btn-info btn-sm w-100 mb-2">
                    <i class="bi bi-cloud-upload"></i> Bulk Upload
                </a>
                <a href="{{ url_for('characters.create') }}" class="btn btn-primary btn-sm w-100">
                    <i class="bi bi-plus-circle"></i> Create Character
                </a>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-robot"></i> Scenario Mode</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-2">
                    <strong>What is Scenario Mode?</strong>
                </p>
                <ul class="small text-muted">
                    <li>Processes your image with AI for enhanced understanding</li>
                    <li>Stores image data in vector database for character interactions</li>
                    <li>Enables advanced AI-powered character responses</li>
                    <li>Requires a detailed description for best results</li>
                </ul>
                <div class="alert alert-info py-2 mb-0">
                    <small>
                        <i class="bi bi-info-circle"></i>
                        Enable this feature to make your images available for AI character interactions and enhanced search capabilities.
                    </small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-file-earmark-code"></i> Example Parameters</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <pre><code>{
  "prompt": "fantasy warrior",
  "negative_prompt": "blurry, low quality",
  "model": "stable-diffusion-xl",
  "steps": 30,
  "cfg_scale": 7.5,
  "width": 1024,
  "height": 1024,
  "seed": 12345,
  "sampler": "DPM++ 2M Karras"
}</code></pre>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pre-select character if provided in URL
    const urlParams = new URLSearchParams(window.location.search);
    const characterId = urlParams.get('character_id');
    if (characterId) {
        const characterSelect = document.querySelector('#character_id');
        if (characterSelect) {
            characterSelect.value = characterId;
        }
    }

    // JSON validation for generation parameters field
    const parametersField = document.querySelector('#generation_parameters');
    if (parametersField) {
        parametersField.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value && value !== '') {
                try {
                    JSON.parse(value);
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    }

    // Enhanced file input handling for upload area
    const uploadArea = document.querySelector('.upload-area');
    const fileInput = document.querySelector('#image_file');

    if (uploadArea && fileInput) {
        // Handle file input change to show preview and update upload area
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, GIF, or WebP).');
                    this.value = '';
                    return;
                }

                // Update upload area to show selected file
                uploadArea.innerHTML = `
                    <div class="selected-file-info">
                        <i class="bi bi-file-earmark-image fs-1 text-success"></i>
                        <p class="mt-2 mb-1"><strong>Selected:</strong> ${file.name}</p>
                        <small class="text-muted">Size: ${window.CMS ? window.CMS.formatFileSize(file.size) : formatFileSize(file.size)}</small>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="changeFile()">
                                <i class="bi bi-arrow-repeat"></i> Change File
                            </button>
                        </div>
                    </div>
                `;

                // Create image preview if it doesn't exist
                let preview = uploadArea.parentNode.querySelector('.image-preview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.className = 'image-preview mt-3';
                    uploadArea.parentNode.appendChild(preview);
                }

                // Show image preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <div class="text-center">
                            <img src="${e.target.result}" class="img-thumbnail" style="max-width: 300px; max-height: 300px;">
                            <div class="mt-2">
                                <small class="text-muted">Preview of selected image</small>
                            </div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                // Reset upload area if no file selected
                resetUploadArea();
            }
        });
    }

    // Function to change file (reset file input)
    window.changeFile = function() {
        if (fileInput) {
            fileInput.value = '';
            resetUploadArea();
            // Remove preview
            const preview = document.querySelector('.image-preview');
            if (preview) {
                preview.remove();
            }
        }
    };

    // Function to reset upload area to original state
    function resetUploadArea() {
        if (uploadArea) {
            uploadArea.innerHTML = `
                <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                <p class="mt-2 mb-0">Click to select or drag and drop an image</p>
                <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP (Max 16MB)</small>
            `;
        }
    }

    // Utility function for file size formatting (fallback if CMS not available)
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Enhanced form submission with loading overlay
    const uploadForm = document.querySelector('form[enctype="multipart/form-data"]');
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            const fileInput = this.querySelector('input[type="file"]');
            const characterSelect = this.querySelector('#character_id');

            // Validate required fields
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('Please select an image file to upload.');
                return false;
            }

            if (!characterSelect.value) {
                e.preventDefault();
                alert('Please select a character.');
                return false;
            }

            const descriptionField = this.querySelector('#description');
            if (!descriptionField.value.trim()) {
                e.preventDefault();
                alert('Please provide a description for the image.');
                descriptionField.focus();
                return false;
            }

            // Show loading overlay with appropriate message
            const scenarioMode = this.querySelector('#scenario_mode');
            const loadingMessage = scenarioMode && scenarioMode.checked ?
                'Uploading image and processing with AI...' : 'Uploading image...';

            try {
                if (typeof window.showLoadingOverlay === 'function') {
                    window.showLoadingOverlay(loadingMessage);
                } else {
                    console.warn('showLoadingOverlay function not available');
                }
            } catch (error) {
                console.error('Error showing loading overlay:', error);
            }

            // The form will submit normally, and the page will redirect
            // The loading overlay will be removed when the new page loads
        });
    }

    // Handle scenario mode toggle
    const scenarioModeCheckbox = document.querySelector('#scenario_mode');
    if (scenarioModeCheckbox) {
        scenarioModeCheckbox.addEventListener('change', function() {
            const card = this.closest('.card');
            const cardBody = card.querySelector('.card-body');

            if (this.checked) {
                card.classList.remove('border-info');
                card.classList.add('border-success');
                card.querySelector('.card-header').classList.remove('bg-info');
                card.querySelector('.card-header').classList.add('bg-success');

                // Add additional info when enabled
                let infoDiv = cardBody.querySelector('.scenario-info');
                if (!infoDiv) {
                    infoDiv = document.createElement('div');
                    infoDiv.className = 'scenario-info mt-2 p-2 bg-light rounded';
                    infoDiv.innerHTML = `
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i>
                            <strong>Scenario Mode Enabled:</strong> This image will be processed with AI and stored in the vector database for enhanced character interactions.
                        </small>
                    `;
                    cardBody.appendChild(infoDiv);
                }
            } else {
                card.classList.remove('border-success');
                card.classList.add('border-info');
                card.querySelector('.card-header').classList.remove('bg-success');
                card.querySelector('.card-header').classList.add('bg-info');

                // Remove additional info when disabled
                const infoDiv = cardBody.querySelector('.scenario-info');
                if (infoDiv) {
                    infoDiv.remove();
                }
            }
        });
    }
});
</script>
{% endblock %}
