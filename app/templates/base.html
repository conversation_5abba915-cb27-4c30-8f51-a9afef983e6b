<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Character CMS - Manage characters and their generated images with ease">
    <meta name="keywords" content="character management, image gallery, CMS, AI art, character database">
    <meta name="author" content="Character CMS">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{% block title %}Character CMS{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👥</text></svg>">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block head %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="bi bi-person-badge"></i> Character CMS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('characters.index') }}">
                            <i class="bi bi-people"></i> Characters
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="imagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-images"></i> Images
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="imagesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('images.index') }}">
                                <i class="bi bi-images"></i> Local Images
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('images.qdrant_index') }}">
                                <i class="bi bi-cloud-arrow-down"></i> Vector Images
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <span class="nav-link text-muted">
                            <i class="bi bi-lock"></i> Login to browse content
                        </span>
                    </li>
                    {% endif %}
                    {% if current_user.is_authenticated and current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.index') }}">
                            <i class="bi bi-gear"></i> Admin
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- Search Form -->
                {% if current_user.is_authenticated %}
                <form class="d-flex me-3" method="GET" action="{{ url_for('main.search') }}">
                    <input class="form-control me-2" type="search" name="q" placeholder="Search..."
                           value="{{ request.args.get('q', '') }}">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                </form>
                {% endif %}
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> {{ current_user.username }}
                                {% if current_user.is_admin %}
                                <span class="badge bg-danger ms-1">Admin</span>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                {% if user_permissions.can_create_character %}
                                <li><a class="dropdown-item" href="{{ url_for('characters.create') }}">
                                    <i class="bi bi-plus-circle"></i> New Character
                                </a></li>
                                {% endif %}
                                {% if user_permissions.can_upload_image %}
                                <li><a class="dropdown-item" href="{{ url_for('images.upload') }}">
                                    <i class="bi bi-upload"></i> Upload Image
                                </a></li>
                                {% endif %}
                                {% if user_permissions.can_bulk_upload %}
                                <li><a class="dropdown-item" href="{{ url_for('images.bulk_upload') }}">
                                    <i class="bi bi-cloud-upload"></i> Bulk Upload
                                </a></li>
                                {% endif %}
                                {% if user_permissions.can_access_admin %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('admin.index') }}">
                                    <i class="bi bi-gear"></i> Admin Panel
                                </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">
                                <i class="bi bi-box-arrow-in-right"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.register') }}">
                                <i class="bi bi-person-plus"></i> Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container mt-4 mb-5">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-auto py-4 border-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; 2024 Character CMS. Built with Flask and Bootstrap.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        <i class="bi bi-shield-check"></i> Secure & Private
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
