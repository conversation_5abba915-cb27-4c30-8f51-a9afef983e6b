{% extends "base.html" %}

{% block title %}Characters - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-people"></i> Characters</h1>
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('characters.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Create Character
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Search characters...">
                    </div>
                    <div class="col-md-4">
                        <label for="tag" class="form-label">Filter by Tag</label>
                        <select class="form-select" id="tag" name="tag">
                            <option value="">All Tags</option>
                            {% for tag in all_tags %}
                            <option value="{{ tag.id }}" {% if current_tag == tag.id %}selected{% endif %}>
                                {{ tag.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="bi bi-search"></i> Filter
                        </button>
                        <a href="{{ url_for('characters.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Characters Grid -->
{% if characters_with_data %}
<div class="row">
    {% for char_data in characters_with_data %}
    {% set character = char_data.character %}
    <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
        <div class="card h-100">
            <!-- Character Avatar Container with proper positioning -->
            <div class="position-relative" style="height: 200px; overflow: hidden;">
                <!-- Character Avatar with Qdrant fallback -->
                {% if char_data.qdrant_avatar %}
                <img src="{{ char_data.qdrant_avatar }}"
                     class="card-img-top w-100 h-100" style="object-fit: cover; object-position: center;"
                     alt="{{ character.name }}"
                     onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="card-img-top bg-light d-flex align-items-center justify-content-center w-100 h-100"
                     style="display: none; position: absolute; top: 0; left: 0;">
                    <i class="bi bi-person fs-1 text-muted"></i>
                </div>
                {% elif character.thumbnail %}
                <img src="{{ url_for('images.serve_image', filename=character.thumbnail) }}"
                     class="card-img-top w-100 h-100" style="object-fit: cover; object-position: center;"
                     alt="{{ character.name }}">
                {% else %}
                <div class="card-img-top bg-light d-flex align-items-center justify-content-center w-100 h-100">
                    <i class="bi bi-person fs-1 text-muted"></i>
                </div>
                {% endif %}

                <!-- Image Count Badge -->
                {% if qdrant_enabled and char_data.qdrant_image_count > 0 %}
                <div class="position-absolute top-0 end-0 p-2">
                    <span class="badge bg-primary bg-opacity-90">
                        <i class="bi bi-images"></i> {{ char_data.qdrant_image_count }}
                    </span>
                </div>
                {% endif %}
            </div>
            
            <div class="card-body">
                <h5 class="card-title">{{ character.name }}</h5>
                {% if character.description %}
                <p class="card-text text-muted">
                    {{ character.description[:100] }}{% if character.description|length > 100 %}...{% endif %}
                </p>
                {% endif %}
                
                <!-- Tags -->
                {% if character.tags %}
                <div class="mb-2">
                    {% for tag in character.tags %}
                    <span class="badge me-1" style="background-color: {{ tag.color }};">
                        {{ tag.name }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <small class="text-muted">
                    <i class="bi bi-images"></i> {{ character.get_generated_images_count() }} local
                    {% if qdrant_enabled and char_data.qdrant_image_count > 0 %}
                    | <i class="bi bi-cloud"></i> {{ char_data.qdrant_image_count }} vector
                    {% endif %}
                    <br>
                    <i class="bi bi-calendar"></i> {{ character.created_at.strftime('%Y-%m-%d') }}
                </small>
            </div>
            
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('characters.view', id=character.id) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i> View
                    </a>
                    {% if can_edit_character(character) %}
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('characters.edit', id=character.id) }}"
                           class="btn btn-outline-secondary btn-sm"
                           title="Edit character">
                            <i class="bi bi-pencil"></i>
                        </a>
                        {% if can_delete_character(character) %}
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="confirmDelete('{{ character.name }}', '{{ url_for('characters.delete', id=character.id) }}')"
                                title="Delete character">
                            <i class="bi bi-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if pagination.pages > 1 %}
<nav aria-label="Characters pagination">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('characters.index', page=pagination.prev_num, search=search_query, tag=current_tag) }}">
                Previous
            </a>
        </li>
        {% endif %}
        
        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('characters.index', page=page_num, search=search_query, tag=current_tag) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('characters.index', page=pagination.next_num, search=search_query, tag=current_tag) }}">
                Next
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="bi bi-people fs-1 text-muted"></i>
    <h3 class="text-muted mt-3">No characters found</h3>
    {% if search_query or current_tag %}
    <p class="text-muted">Try adjusting your search criteria.</p>
    <a href="{{ url_for('characters.index') }}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> View All Characters
    </a>
    {% else %}
    <p class="text-muted">Get started by creating your first character.</p>
    {% if current_user.is_authenticated %}
    <a href="{{ url_for('characters.create') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Create Character
    </a>
    {% endif %}
    {% endif %}
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the character "<span id="deleteCharacterName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone and will also delete all associated images.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(characterName, deleteUrl) {
    document.getElementById('deleteCharacterName').textContent = characterName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
