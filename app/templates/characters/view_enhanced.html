{% extends "base.html" %}

{% block title %}{{ character.name }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-person-badge"></i> {{ character.name }}
                {% if character.age %}
                <small class="text-muted">({{ character.age }} years old)</small>
                {% endif %}
            </h1>
            <div>
                <a href="{{ url_for('characters.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Characters
                </a>

                <!-- Debug info for edit button -->
                {% if current_user.is_authenticated %}
                    <div class="alert alert-success mt-2" style="font-size: 12px;">
                        <strong>✅ Quyền chỉnh sửa:</strong><br>
                        User: {{ current_user.username }} (ID: {{ current_user.id }})<br>
                        <PERSON><PERSON> đăng nhập: {{ current_user.is_authenticated }}<br>
                        <PERSON><PERSON> thể chỉnh sửa: {{ can_edit_character(character) }}<br>
                        <em>Tất cả user đã đăng nhập đều có thể chỉnh sửa nhân vật!</em>
                    </div>
                {% endif %}

                {% if can_edit_character(character) %}
                <button type="button" class="btn btn-primary" onclick="openCharacterEditModal()">
                    <i class="bi bi-pencil-square"></i> Edit Character
                </button>
                {% else %}
                <div class="alert alert-warning mt-2">
                    <i class="bi bi-exclamation-triangle"></i>
                    Bạn cần đăng nhập để chỉnh sửa nhân vật.
                    <a href="{{ url_for('auth.login') }}" class="btn btn-sm btn-primary ms-2">
                        <i class="bi bi-box-arrow-in-right"></i> Đăng nhập
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Character Information -->
    <div class="col-lg-8">
        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person"></i> Basic Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <!-- Character ID -->
                        <div class="mb-2">
                            <strong>Character ID:</strong>
                            <span class="editable-field" data-field="character_id" data-type="text">
                                {% if character.character_id %}
                                <code>{{ character.character_id }}</code>
                                {% else %}
                                <em class="text-muted">Not set</em>
                                {% endif %}
                                {% if can_edit_character(character) %}
                                <button class="btn btn-sm btn-outline-secondary ms-2 edit-btn" onclick="editField('character_id', '{{ character.character_id or '' }}', 'text')">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                {% endif %}
                            </span>
                        </div>

                        <!-- Occupation -->
                        <div class="mb-2">
                            <strong>Occupation:</strong>
                            <span class="editable-field" data-field="occupation" data-type="text">
                                {% if character.occupation %}
                                {{ character.occupation }}
                                {% else %}
                                <em class="text-muted">Not set</em>
                                {% endif %}
                                {% if can_edit_character(character) %}
                                <button class="btn btn-sm btn-outline-secondary ms-2 edit-btn" onclick="editField('occupation', '{{ character.occupation or '' }}', 'text')">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                {% endif %}
                            </span>
                        </div>

                        <!-- Location -->
                        <div class="mb-2">
                            <strong>Location:</strong>
                            <span class="editable-field" data-field="country" data-type="text">
                                {% if character.country %}
                                {{ character.country }}
                                {% else %}
                                <em class="text-muted">Not set</em>
                                {% endif %}
                                {% if can_edit_character(character) %}
                                <button class="btn btn-sm btn-outline-secondary ms-2 edit-btn" onclick="editField('country', '{{ character.country or '' }}', 'text')">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                {% endif %}
                            </span>
                        </div>

                        <!-- Gender -->
                        <div class="mb-2">
                            <strong>Gender:</strong>
                            <span class="editable-field" data-field="gender" data-type="select">
                                {% if character.gender %}
                                {{ character.gender }}
                                {% else %}
                                <em class="text-muted">Not set</em>
                                {% endif %}
                                {% if can_edit_character(character) %}
                                <button class="btn btn-sm btn-outline-secondary ms-2 edit-btn" onclick="editField('gender', '{{ character.gender or '' }}', 'select', ['', 'Male', 'Female', 'Non-binary', 'Other'])">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        {% if character.hobbies %}
                        <p><strong>Hobbies:</strong> {{ character.hobbies }}</p>
                        {% endif %}
                        {% if character.types %}
                        <p><strong>Type:</strong> <span class="badge bg-primary">{{ character.types }}</span></p>
                        {% endif %}
                        {% if character.timezone %}
                        <p><strong>Timezone:</strong> UTC{{ '+' if character.timezone >= 0 else '' }}{{ character.timezone }}</p>
                        {% endif %}
                        {% if character.voice_id %}
                        <p><strong>Voice ID:</strong> <code>{{ character.voice_id }}</code></p>
                        {% endif %}
                    </div>
                </div>
                
                {% if character.bio %}
                <div class="mt-3">
                    <strong>Bio:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ character.bio }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Physical Appearance Card -->
        {% if character.hair_color or character.eye_color or character.skin_color or character.face_detail or character.body_detail %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-palette"></i> Physical Appearance</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        {% if character.hair_color %}
                        <p><strong>Hair Color:</strong> {{ character.hair_color }}</p>
                        {% endif %}
                        {% if character.hair_style %}
                        <p><strong>Hair Style:</strong> {{ character.hair_style }}</p>
                        {% endif %}
                        {% if character.eye_color %}
                        <p><strong>Eye Color:</strong> {{ character.eye_color }}</p>
                        {% endif %}
                        {% if character.eye_type %}
                        <p><strong>Eye Type:</strong> {{ character.eye_type }}</p>
                        {% endif %}
                        {% if character.skin_color %}
                        <p><strong>Skin Color:</strong> {{ character.skin_color }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if character.face_detail %}
                        <div class="mb-3">
                            <strong>Face Details:</strong>
                            <div class="mt-1 p-2 bg-light rounded small">
                                {{ character.face_detail }}
                            </div>
                        </div>
                        {% endif %}
                        {% if character.body_detail %}
                        <div class="mb-3">
                            <strong>Body Details:</strong>
                            <div class="mt-1 p-2 bg-light rounded small">
                                {{ character.body_detail }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Personality & Roleplay Card -->
        {% if character.personality_roleplay or character.style_roleplay or character.scenario_information %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-chat-heart"></i> Personality & Roleplay</h5>
            </div>
            <div class="card-body">
                {% if character.personality_roleplay %}
                <div class="mb-3">
                    <strong>Personality:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ character.personality_roleplay }}
                    </div>
                </div>
                {% endif %}
                
                {% if character.style_roleplay %}
                <div class="mb-3">
                    <strong>Communication Style:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ character.style_roleplay }}
                    </div>
                </div>
                {% endif %}
                
                {% if character.scenario_information %}
                <div class="mb-3">
                    <strong>Default Scenario:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ character.scenario_information }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- AI Generation Prompts Card -->
        {% if character.prompt_gen_image or character.prompt_negative_gen_image or character.prompt_gen_scenario_image %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-magic"></i> AI Generation Prompts</h5>
            </div>
            <div class="card-body">
                {% if character.prompt_gen_image %}
                <div class="mb-3">
                    <strong>Image Generation Prompt:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        <code>{{ character.prompt_gen_image }}</code>
                    </div>
                </div>
                {% endif %}
                
                {% if character.prompt_negative_gen_image %}
                <div class="mb-3">
                    <strong>Negative Prompt:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        <code>{{ character.prompt_negative_gen_image }}</code>
                    </div>
                </div>
                {% endif %}
                
                {% if character.prompt_gen_scenario_image %}
                <div class="mb-3">
                    <strong>Scenario Image Prompt:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        <code>{{ character.prompt_gen_scenario_image }}</code>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Legacy Information Card -->
        {% if character.description or character.attributes %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-archive"></i> Legacy Information</h5>
            </div>
            <div class="card-body">
                {% if character.description %}
                <div class="mb-3">
                    <strong>Description:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ character.description }}
                    </div>
                </div>
                {% endif %}
                
                {% if character.attributes %}
                <div class="mb-3">
                    <strong>Attributes (JSON):</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        <pre><code>{{ character.attributes }}</code></pre>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Character Image -->
        {% if character.template_image %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">Character Image</h6>
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('images.serve_image', filename=character.template_image) }}" 
                     class="img-fluid rounded" alt="{{ character.name }}" style="max-height: 400px;">
            </div>
        </div>
        {% endif %}

        <!-- Match Rate Roleset -->
        {% if character.get_match_rate_roleset_list() %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-tags"></i> Match Rate Roleset</h6>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-1">
                    {% for tag in character.get_match_rate_roleset_list() %}
                    <span class="badge bg-secondary">{{ tag }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Tags -->
        {% if character.tags %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-tag"></i> Tags</h6>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-1">
                    {% for tag in character.tags %}
                    <span class="badge" style="background-color: {{ tag.color }};">{{ tag.name }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Character Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Character Stats</h6>
            </div>
            <div class="card-body">
                <p><strong>Created:</strong> {{ character.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                <p><strong>Updated:</strong> {{ character.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                <p><strong>Creator:</strong> {{ character.creator.username }}</p>
                <p><strong>Images:</strong> {{ character.generated_images.count() }}</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                {% if can_edit_character(character) %}
                <button type="button" class="btn btn-primary btn-sm w-100 mb-2" onclick="openCharacterEditModal()">
                    <i class="bi bi-pencil-square"></i> Edit Character
                </button>
                {% endif %}
                
                <a href="{{ url_for('images.upload') }}?character_id={{ character.id }}" 
                   class="btn btn-outline-success btn-sm w-100 mb-2">
                    <i class="bi bi-upload"></i> Upload Image
                </a>
                
                <a href="{{ url_for('images.index') }}?character={{ character.id }}" 
                   class="btn btn-outline-info btn-sm w-100 mb-2">
                    <i class="bi bi-images"></i> View Images
                </a>
                
                {% if can_delete_character(character) %}
                <button type="button" class="btn btn-outline-danger btn-sm w-100" 
                        onclick="confirmDelete('{{ character.name }}', '{{ url_for('characters.delete', id=character.id) }}')">
                    <i class="bi bi-trash"></i> Delete Character
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Qdrant Images Section -->
{% if qdrant_enabled %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <h5 class="mb-0">
                        <i class="bi bi-database"></i> Vector Database Images
                        {% if qdrant_stats.total_images %}
                        <span class="badge bg-primary">{{ qdrant_stats.total_images }} total</span>
                        {% endif %}
                    </h5>
                    <div class="d-flex gap-2 flex-wrap">
                        <!-- MongoDB Fields Edit Button -->
                        {% if can_edit_character(character) %}
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="openMongoDBFieldsModal()">
                            <i class="bi bi-pencil-square"></i> Edit All MongoDB Fields
                        </button>
                        {% endif %}

                        <!-- Filter buttons -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="filterQdrantImages()">
                                All
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="filterQdrantImages(false)">
                                SFW
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterQdrantImages(true)">
                                NSFW
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if qdrant_stats.error %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading vector database images: {{ qdrant_stats.error }}
                </div>
                {% elif qdrant_images %}
                <div id="qdrant-images-container" class="row">
                    {% for image in qdrant_images %}
                    <div class="col-md-3 col-sm-4 col-6 mb-3">
                        <div class="card h-100 qdrant-image-card position-relative">
                            <div class="position-relative image-hover-container">
                                {% if image.metadata.image_url %}
                                <img src="{{ image.metadata.image_url }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover; cursor: pointer; transition: transform 0.2s ease;"
                                     onclick="showQdrantImageModal('{{ image.id }}', '{{ image.metadata.image_url }}', {{ image.metadata | tojson | safe }})"
                                     alt="Generated Image">
                                {% else %}
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                     style="height: 200px;">
                                    <i class="bi bi-image fs-1 text-muted"></i>
                                </div>
                                {% endif %}

                                <!-- Hover overlay with delete button -->
                                {% if can_edit_character(character) %}
                                <div class="image-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                                     style="background: rgba(0,0,0,0.7); opacity: 0; transition: opacity 0.3s ease;">
                                    <button type="button" class="btn btn-danger btn-lg"
                                            onclick="event.stopPropagation(); confirmDeleteQdrantImage('{{ image.id }}', '{{ image.metadata.character_id }}')"
                                            title="Delete Image">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </div>
                                {% endif %}

                                <!-- Click to view indicator -->
                                <div class="position-absolute bottom-0 end-0 p-2">
                                    <span class="badge bg-primary bg-opacity-75">
                                        <i class="bi bi-eye"></i> Click to view
                                    </span>
                                </div>
                            </div>

                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    {% if image.metadata.type %}
                                    <span class="badge bg-info">{{ image.metadata.type }}</span>
                                    {% endif %}
                                    {% if image.metadata.type_nsfw %}
                                    <span class="badge bg-danger">NSFW</span>
                                    {% else %}
                                    <span class="badge bg-success">SFW</span>
                                    {% endif %}
                                </div>
                                {% if image.metadata.timestamp %}
                                <small class="text-muted">{{ image.metadata.timestamp }}</small>
                                {% endif %}
                                {% if image.text %}
                                <p class="card-text small text-truncate" title="{{ image.text }}">
                                    {{ image.text[:50] }}{% if image.text|length > 50 %}...{% endif %}
                                </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div id="qdrant-loading" class="text-center d-none">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading images...</p>
                </div>

                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-images fs-1"></i>
                    <p class="mt-2">No vector database images found for this character.</p>
                    {% if character.character_id %}
                    <small>Searching for character ID: <code>{{ character.character_id }}</code></small>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete "<span id="deleteCharacterName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone and will also delete all associated images.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Enhanced Qdrant Image Modal with Zoom -->
{% if qdrant_enabled %}
<div class="modal fade" id="qdrantImageModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Vector Database Image - Full View</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row g-0">
                    <div class="col-lg-8">
                        <!-- Image container with zoom functionality -->
                        <div class="position-relative bg-dark d-flex align-items-center justify-content-center" style="min-height: 500px;">
                            <img id="qdrantModalImage" src="" class="img-fluid" alt="Generated Image"
                                 style="max-height: 80vh; cursor: zoom-in; transition: transform 0.3s ease;"
                                 onclick="toggleImageZoom(this)">

                            <!-- Zoom controls -->
                            <div class="position-absolute top-0 end-0 p-3">
                                <div class="btn-group-vertical" role="group">
                                    <button type="button" class="btn btn-sm btn-light bg-opacity-75" onclick="zoomImage(1.2)" title="Zoom In">
                                        <i class="bi bi-zoom-in"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-light bg-opacity-75" onclick="zoomImage(0.8)" title="Zoom Out">
                                        <i class="bi bi-zoom-out"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-light bg-opacity-75" onclick="resetImageZoom()" title="Reset Zoom">
                                        <i class="bi bi-arrows-angle-contract"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 p-4">
                        <div id="qdrantModalDetails" class="h-100 overflow-auto">
                            <!-- Details will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="openInNewTabBtn">
                    <i class="bi bi-box-arrow-up-right"></i> Open in New Tab
                </button>
                {% if can_edit_character(character) %}
                <button type="button" class="btn btn-danger" id="qdrantModalDeleteBtn" onclick="deleteCurrentQdrantImage()">
                    <i class="bi bi-trash"></i> Delete Image
                </button>
                {% endif %}
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Qdrant Image Delete Confirmation Modal -->
<div class="modal fade" id="deleteQdrantImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this image from the vector database?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmQdrantDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- MongoDB Fields Edit Modal -->
{% if can_edit_character(character) %}
<div class="modal fade" id="mongoDBFieldsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit All MongoDB Fields - {{ character.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="mongoDBFieldsForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <!-- Tabs for field categories -->
                    <ul class="nav nav-tabs" id="mongoFieldTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-fields" type="button" role="tab">
                                <i class="bi bi-person"></i> Basic Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="physical-tab" data-bs-toggle="tab" data-bs-target="#physical-fields" type="button" role="tab">
                                <i class="bi bi-palette"></i> Physical
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="personality-tab" data-bs-toggle="tab" data-bs-target="#personality-fields" type="button" role="tab">
                                <i class="bi bi-chat-heart"></i> Personality
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ai-tab" data-bs-toggle="tab" data-bs-target="#ai-fields" type="button" role="tab">
                                <i class="bi bi-magic"></i> AI Generation
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system-fields" type="button" role="tab">
                                <i class="bi bi-gear"></i> System
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="mongoFieldTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic-fields" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mongo_character_id" class="form-label">Character ID</label>
                                        <input type="text" class="form-control" id="mongo_character_id" name="character_id"
                                               value="{{ character.character_id or '' }}" placeholder="e.g., ayumi">
                                        <div class="form-text">Unique identifier for the character</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_name" class="form-label">Name *</label>
                                        <input type="text" class="form-control" id="mongo_name" name="name"
                                               value="{{ character.name or '' }}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_age" class="form-label">Age</label>
                                        <input type="number" class="form-control" id="mongo_age" name="age"
                                               value="{{ character.age or '' }}" min="1" max="999">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mongo_occupation" class="form-label">Occupation</label>
                                        <input type="text" class="form-control" id="mongo_occupation" name="occupation"
                                               value="{{ character.occupation or '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_country" class="form-label">Country/Location</label>
                                        <input type="text" class="form-control" id="mongo_country" name="country"
                                               value="{{ character.country or '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_hobbies" class="form-label">Hobbies</label>
                                        <input type="text" class="form-control" id="mongo_hobbies" name="hobbies"
                                               value="{{ character.hobbies or '' }}">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_bio" class="form-label">Bio</label>
                                <textarea class="form-control" id="mongo_bio" name="bio" rows="4">{{ character.bio or '' }}</textarea>
                            </div>
                        </div>

                        <!-- Physical Appearance Tab -->
                        <div class="tab-pane fade" id="physical-fields" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mongo_hair_color" class="form-label">Hair Color</label>
                                        <input type="text" class="form-control" id="mongo_hair_color" name="hair_color"
                                               value="{{ character.hair_color or '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_hair_style" class="form-label">Hair Style</label>
                                        <input type="text" class="form-control" id="mongo_hair_style" name="hair_style"
                                               value="{{ character.hair_style or '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_eye_color" class="form-label">Eye Color</label>
                                        <input type="text" class="form-control" id="mongo_eye_color" name="eye_color"
                                               value="{{ character.eye_color or '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_eye_type" class="form-label">Eye Type</label>
                                        <input type="text" class="form-control" id="mongo_eye_type" name="eye_type"
                                               value="{{ character.eye_type or '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_skin_color" class="form-label">Skin Color</label>
                                        <input type="text" class="form-control" id="mongo_skin_color" name="skin_color"
                                               value="{{ character.skin_color or '' }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mongo_face_detail" class="form-label">Face Details</label>
                                        <textarea class="form-control" id="mongo_face_detail" name="face_detail" rows="3">{{ character.face_detail or '' }}</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_body_detail" class="form-label">Body Details</label>
                                        <textarea class="form-control" id="mongo_body_detail" name="body_detail" rows="3">{{ character.body_detail or '' }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personality & Roleplay Tab -->
                        <div class="tab-pane fade" id="personality-fields" role="tabpanel">
                            <div class="mb-3">
                                <label for="mongo_personality_roleplay" class="form-label">Personality & Roleplay</label>
                                <textarea class="form-control" id="mongo_personality_roleplay" name="personality_roleplay" rows="4">{{ character.personality_roleplay or '' }}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_style_roleplay" class="form-label">Communication Style</label>
                                <textarea class="form-control" id="mongo_style_roleplay" name="style_roleplay" rows="3">{{ character.style_roleplay or '' }}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_nsfw_style" class="form-label">NSFW Style</label>
                                <textarea class="form-control" id="mongo_nsfw_style" name="nsfw_style" rows="3">{{ character.nsfw_style or '' }}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_scenario_information" class="form-label">Scenario Information</label>
                                <textarea class="form-control" id="mongo_scenario_information" name="scenario_information" rows="4">{{ character.scenario_information or '' }}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_match_rate_roleset" class="form-label">Match Rate Roleset</label>
                                <input type="text" class="form-control" id="mongo_match_rate_roleset" name="match_rate_roleset"
                                       value="{{ character.match_rate_roleset or '' }}" placeholder="Comma-separated tags">
                            </div>
                        </div>

                        <!-- AI Generation Tab -->
                        <div class="tab-pane fade" id="ai-fields" role="tabpanel">
                            <div class="mb-3">
                                <label for="mongo_prompt_gen_image" class="form-label">Image Generation Prompt</label>
                                <textarea class="form-control" id="mongo_prompt_gen_image" name="prompt_gen_image" rows="4">{{ character.prompt_gen_image or '' }}</textarea>
                                <div class="form-text">Positive prompt for AI image generation</div>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_prompt_negative_gen_image" class="form-label">Negative Image Prompt</label>
                                <textarea class="form-control" id="mongo_prompt_negative_gen_image" name="prompt_negative_gen_image" rows="3">{{ character.prompt_negative_gen_image or '' }}</textarea>
                                <div class="form-text">Negative prompt for AI image generation</div>
                            </div>
                            <div class="mb-3">
                                <label for="mongo_prompt_gen_scenario_image" class="form-label">Scenario Image Prompt</label>
                                <textarea class="form-control" id="mongo_prompt_gen_scenario_image" name="prompt_gen_scenario_image" rows="3">{{ character.prompt_gen_scenario_image or '' }}</textarea>
                                <div class="form-text">Prompt for generating scenario images</div>
                            </div>
                        </div>

                        <!-- System Information Tab -->
                        <div class="tab-pane fade" id="system-fields" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mongo_gender" class="form-label">Gender</label>
                                        <select class="form-select" id="mongo_gender" name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="Male" {% if character.gender == 'Male' %}selected{% endif %}>Male</option>
                                            <option value="Female" {% if character.gender == 'Female' %}selected{% endif %}>Female</option>
                                            <option value="Non-binary" {% if character.gender == 'Non-binary' %}selected{% endif %}>Non-binary</option>
                                            <option value="Other" {% if character.gender == 'Other' %}selected{% endif %}>Other</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_timezone" class="form-label">Timezone</label>
                                        <input type="number" class="form-control" id="mongo_timezone" name="timezone"
                                               value="{{ character.timezone or '' }}" min="-12" max="14" placeholder="e.g., 9 for JST">
                                        <div class="form-text">Timezone offset (e.g., 9 for JST, -5 for EST)</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mongo_types" class="form-label">Character Type</label>
                                        <select class="form-select" id="mongo_types" name="types">
                                            <option value="">Select Type</option>
                                            <option value="anime" {% if character.types == 'anime' %}selected{% endif %}>Anime</option>
                                            <option value="realistic" {% if character.types == 'realistic' %}selected{% endif %}>Realistic</option>
                                            <option value="cartoon" {% if character.types == 'cartoon' %}selected{% endif %}>Cartoon</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mongo_voice_id" class="form-label">Voice ID</label>
                                        <input type="text" class="form-control" id="mongo_voice_id" name="voice_id"
                                               value="{{ character.voice_id or '' }}" placeholder="Voice identifier for TTS">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <span id="mongoFieldsSaveStatus" class="text-muted"></span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="saveMongoFieldsBtn" onclick="saveAllMongoDBFields()">
                            <span class="save-btn-text">
                                <i class="bi bi-check"></i> Save All Changes
                            </span>
                            <span class="save-btn-loading d-none">
                                <span class="spinner-border spinner-border-sm" role="status"></span> Saving...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Comprehensive Character Edit Modal -->
{% if can_edit_character(character) %}
<div class="modal fade character-edit-modal" id="characterEditModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil-square"></i> Edit Character - {{ character.name }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="characterEditForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs mb-4" id="characterEditTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab"
                                    data-bs-target="#basic-info-pane" type="button" role="tab">
                                <i class="bi bi-person"></i> Basic Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="physical-tab" data-bs-toggle="tab"
                                    data-bs-target="#physical-pane" type="button" role="tab">
                                <i class="bi bi-palette"></i> Physical
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="personality-tab" data-bs-toggle="tab"
                                    data-bs-target="#personality-pane" type="button" role="tab">
                                <i class="bi bi-chat-heart"></i> Personality
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ai-prompts-tab" data-bs-toggle="tab"
                                    data-bs-target="#ai-prompts-pane" type="button" role="tab">
                                <i class="bi bi-magic"></i> AI Prompts
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab"
                                    data-bs-target="#system-pane" type="button" role="tab">
                                <i class="bi bi-gear"></i> System
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="characterEditTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic-info-pane" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_name" class="form-label">Name *</label>
                                        <input type="text" class="form-control" id="edit_name" name="name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_age" class="form-label">Age</label>
                                        <input type="number" class="form-control" id="edit_age" name="age" min="1" max="999">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_occupation" class="form-label">Occupation</label>
                                        <input type="text" class="form-control" id="edit_occupation" name="occupation">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_country" class="form-label">Country/Location</label>
                                        <input type="text" class="form-control" id="edit_country" name="country">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_gender" class="form-label">Gender</label>
                                        <select class="form-select" id="edit_gender" name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Non-binary">Non-binary</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_hobbies" class="form-label">Hobbies</label>
                                        <textarea class="form-control" id="edit_hobbies" name="hobbies" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_bio" class="form-label">Bio</label>
                                        <textarea class="form-control" id="edit_bio" name="bio" rows="4"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Physical Appearance Tab -->
                        <div class="tab-pane fade" id="physical-pane" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_hair_color" class="form-label">Hair Color</label>
                                        <input type="text" class="form-control" id="edit_hair_color" name="hair_color">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_hair_style" class="form-label">Hair Style</label>
                                        <input type="text" class="form-control" id="edit_hair_style" name="hair_style">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_eye_color" class="form-label">Eye Color</label>
                                        <input type="text" class="form-control" id="edit_eye_color" name="eye_color">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_eye_type" class="form-label">Eye Type</label>
                                        <input type="text" class="form-control" id="edit_eye_type" name="eye_type">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_skin_color" class="form-label">Skin Color</label>
                                        <input type="text" class="form-control" id="edit_skin_color" name="skin_color">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_face_detail" class="form-label">Face Details</label>
                                        <textarea class="form-control" id="edit_face_detail" name="face_detail" rows="4"></textarea>
                                        <div class="form-text">Detailed description of facial features</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_body_detail" class="form-label">Body Details</label>
                                        <textarea class="form-control" id="edit_body_detail" name="body_detail" rows="4"></textarea>
                                        <div class="form-text">Detailed description of body characteristics</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personality & Roleplay Tab -->
                        <div class="tab-pane fade" id="personality-pane" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_personality_roleplay" class="form-label">Personality</label>
                                        <textarea class="form-control" id="edit_personality_roleplay" name="personality_roleplay" rows="5"></textarea>
                                        <div class="form-text">Character's personality traits and behavior patterns</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_style_roleplay" class="form-label">Communication Style</label>
                                        <textarea class="form-control" id="edit_style_roleplay" name="style_roleplay" rows="4"></textarea>
                                        <div class="form-text">How the character communicates and interacts</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_nsfw_style" class="form-label">NSFW Style</label>
                                        <textarea class="form-control" id="edit_nsfw_style" name="nsfw_style" rows="4"></textarea>
                                        <div class="form-text">Adult content interaction style (if applicable)</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_scenario_information" class="form-label">Default Scenario</label>
                                        <textarea class="form-control" id="edit_scenario_information" name="scenario_information" rows="5"></textarea>
                                        <div class="form-text">Default scenario or setting for interactions</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Generation Prompts Tab -->
                        <div class="tab-pane fade" id="ai-prompts-pane" role="tabpanel">
                            <div class="mb-3">
                                <label for="edit_prompt_gen_image" class="form-label">Image Generation Prompt</label>
                                <textarea class="form-control" id="edit_prompt_gen_image" name="prompt_gen_image" rows="4"></textarea>
                                <div class="form-text">Prompt used for generating character images</div>
                            </div>
                            <div class="mb-3">
                                <label for="edit_prompt_negative_gen_image" class="form-label">Negative Image Prompt</label>
                                <textarea class="form-control" id="edit_prompt_negative_gen_image" name="prompt_negative_gen_image" rows="3"></textarea>
                                <div class="form-text">Negative prompt to avoid unwanted elements in generated images</div>
                            </div>
                            <div class="mb-3">
                                <label for="edit_prompt_gen_scenario_image" class="form-label">Scenario Image Prompt</label>
                                <textarea class="form-control" id="edit_prompt_gen_scenario_image" name="prompt_gen_scenario_image" rows="4"></textarea>
                                <div class="form-text">Prompt for generating scenario-specific images</div>
                            </div>
                        </div>

                        <!-- System Fields Tab -->
                        <div class="tab-pane fade" id="system-pane" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_character_id" class="form-label">Character ID</label>
                                        <input type="text" class="form-control" id="edit_character_id" name="character_id">
                                        <div class="form-text">Unique identifier for the character</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_timezone" class="form-label">Timezone</label>
                                        <input type="number" class="form-control" id="edit_timezone" name="timezone" min="-12" max="14">
                                        <div class="form-text">UTC offset (-12 to +14)</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_types" class="form-label">Character Type</label>
                                        <select class="form-select" id="edit_types" name="types">
                                            <option value="">Select Type</option>
                                            <option value="anime">Anime</option>
                                            <option value="realistic">Realistic</option>
                                            <option value="fantasy">Fantasy</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_voice_id" class="form-label">Voice ID</label>
                                        <input type="text" class="form-control" id="edit_voice_id" name="voice_id">
                                        <div class="form-text">Voice identifier for text-to-speech</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_match_rate_roleset" class="form-label">Match Rate Roleset</label>
                                        <textarea class="form-control" id="edit_match_rate_roleset" name="match_rate_roleset" rows="3"></textarea>
                                        <div class="form-text">Comma-separated tags for matching algorithms</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Status Display -->
                <div id="characterSaveStatus" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCharacterBtn" onclick="saveCharacterChanges()">
                    <span class="save-btn-text">
                        <i class="bi bi-check-lg"></i> Save All Changes
                    </span>
                    <span class="save-btn-loading d-none">
                        <span class="spinner-border spinner-border-sm" role="status"></span> Saving...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
/* Enhanced image hover effects */
.image-hover-container:hover .image-overlay {
    opacity: 1 !important;
}

.image-hover-container:hover img {
    transform: scale(1.05);
}

.qdrant-image-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.qdrant-image-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* MongoDB modal enhancements */
.nav-tabs .nav-link {
    border-radius: 0.375rem 0.375rem 0 0;
}

.nav-tabs .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    padding: 1.5rem;
    background: white;
}

/* Image zoom modal enhancements */
#qdrantModalImage {
    max-width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.modal-xl .modal-body {
    padding: 0;
}

/* Loading states */
.save-btn-loading .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Character Edit Modal Enhancements */
.character-edit-modal .nav-tabs .nav-link {
    border-radius: 0.375rem 0.375rem 0 0;
    font-weight: 500;
}

.character-edit-modal .nav-tabs .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.character-edit-modal .tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    padding: 1.5rem;
    background: white;
    min-height: 400px;
}

.character-edit-modal .form-label {
    font-weight: 600;
    color: #495057;
}

.character-edit-modal .form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.character-edit-modal .form-control:focus,
.character-edit-modal .form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Loading states for character edit */
.character-edit-modal .save-btn-loading .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Validation feedback */
.character-edit-modal .is-invalid {
    border-color: #dc3545;
}

.character-edit-modal .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-xl {
        margin: 0.5rem;
    }

    .modal-xl .modal-dialog {
        max-width: none;
        width: auto;
    }

    .image-hover-container .image-overlay {
        opacity: 1;
    }

    .character-edit-modal .tab-content {
        padding: 1rem;
        min-height: 300px;
    }

    .character-edit-modal .nav-tabs {
        flex-wrap: wrap;
    }

    .character-edit-modal .nav-tabs .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
// Define character edit functions first to ensure they're available
function openCharacterEditModal() {
    console.log('openCharacterEditModal called');
    try {
        // Populate form with current character data
        populateCharacterEditForm();
        const modalElement = document.getElementById('characterEditModal');
        if (!modalElement) {
            throw new Error('Character edit modal not found');
        }
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal opened successfully');
    } catch (error) {
        console.error('Error opening character edit modal:', error);
        alert('Error opening edit modal: ' + error.message);
    }
}

function populateCharacterEditForm() {
    try {
        // Use safe character data object for JavaScript
        const character = {
            id: {{ character.id }},
            name: {{ (character.name or '') | tojson | safe }},
            age: {{ character.age or 'null' }},
            occupation: {{ (character.occupation or '') | tojson | safe }},
            country: {{ (character.country or '') | tojson | safe }},
            gender: {{ (character.gender or '') | tojson | safe }},
            hobbies: {{ (character.hobbies or '') | tojson | safe }},
            hair_color: {{ (character.hair_color or '') | tojson | safe }},
            hair_style: {{ (character.hair_style or '') | tojson | safe }},
            eye_color: {{ (character.eye_color or '') | tojson | safe }},
            eye_type: {{ (character.eye_type or '') | tojson | safe }},
            face_detail: {{ (character.face_detail or '') | tojson | safe }},
            body_detail: {{ (character.body_detail or '') | tojson | safe }},
            skin_color: {{ (character.skin_color or '') | tojson | safe }},
            personality_roleplay: {{ (character.personality_roleplay or '') | tojson | safe }},
            style_roleplay: {{ (character.style_roleplay or '') | tojson | safe }},
            nsfw_style: {{ (character.nsfw_style or '') | tojson | safe }},
            bio: {{ (character.bio or '') | tojson | safe }},
            scenario_information: {{ (character.scenario_information or '') | tojson | safe }},
            prompt_gen_image: {{ (character.prompt_gen_image or '') | tojson | safe }},
            prompt_negative_gen_image: {{ (character.prompt_negative_gen_image or '') | tojson | safe }},
            prompt_gen_scenario_image: {{ (character.prompt_gen_scenario_image or '') | tojson | safe }},
            character_id: {{ (character.character_id or '') | tojson | safe }},
            timezone: {{ character.timezone or 0 }},
            types: {{ (character.types or '') | tojson | safe }},
            voice_id: {{ (character.voice_id or '') | tojson | safe }},
            match_rate_roleset: {{ (character.match_rate_roleset or '') | tojson | safe }}
        };

        // Populate all form fields
        const fieldMappings = {
            'name': character.name || '',
            'age': character.age || '',
            'occupation': character.occupation || '',
            'country': character.country || '',
            'gender': character.gender || '',
            'hobbies': character.hobbies || '',
            'hair_color': character.hair_color || '',
            'hair_style': character.hair_style || '',
            'eye_color': character.eye_color || '',
            'eye_type': character.eye_type || '',
            'face_detail': character.face_detail || '',
            'body_detail': character.body_detail || '',
            'skin_color': character.skin_color || '',
            'personality_roleplay': character.personality_roleplay || '',
            'style_roleplay': character.style_roleplay || '',
            'nsfw_style': character.nsfw_style || '',
            'bio': character.bio || '',
            'scenario_information': character.scenario_information || '',
            'prompt_gen_image': character.prompt_gen_image || '',
            'prompt_negative_gen_image': character.prompt_negative_gen_image || '',
            'prompt_gen_scenario_image': character.prompt_gen_scenario_image || '',
            'character_id': character.character_id || '',
            'timezone': character.timezone || '',
            'types': character.types || '',
            'voice_id': character.voice_id || '',
            'match_rate_roleset': character.match_rate_roleset || ''
        };

        for (const [field, value] of Object.entries(fieldMappings)) {
            const element = document.getElementById(`edit_${field}`);
            if (element) {
                element.value = value;
                // Clear any previous validation states
                element.classList.remove('is-invalid');
                const feedback = element.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.remove();
                }
            }
        }
        console.log('Form populated successfully');
    } catch (error) {
        console.error('Error populating form:', error);
    }
}

function validateCharacterForm() {
    const form = document.getElementById('characterEditForm');
    let isValid = true;

    // Clear previous validation states
    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

    // Validate required fields
    const nameField = document.getElementById('edit_name');
    if (!nameField.value.trim()) {
        showFieldError(nameField, 'Name is required');
        isValid = false;
    } else if (nameField.value.length > 100) {
        showFieldError(nameField, 'Name cannot exceed 100 characters');
        isValid = false;
    }

    // Validate age
    const ageField = document.getElementById('edit_age');
    if (ageField.value && (ageField.value < 1 || ageField.value > 999)) {
        showFieldError(ageField, 'Age must be between 1 and 999');
        isValid = false;
    }

    // Validate timezone
    const timezoneField = document.getElementById('edit_timezone');
    if (timezoneField.value && (timezoneField.value < -12 || timezoneField.value > 14)) {
        showFieldError(timezoneField, 'Timezone must be between -12 and +14');
        isValid = false;
    }

    // Validate text field lengths
    const textFieldLimits = {
        'edit_occupation': 100,
        'edit_country': 100,
        'edit_hobbies': 500,
        'edit_hair_color': 50,
        'edit_hair_style': 50,
        'edit_eye_color': 50,
        'edit_eye_type': 50,
        'edit_skin_color': 50,
        'edit_face_detail': 1000,
        'edit_body_detail': 1000,
        'edit_personality_roleplay': 2000,
        'edit_style_roleplay': 1000,
        'edit_nsfw_style': 1000,
        'edit_bio': 2000,
        'edit_scenario_information': 2000,
        'edit_prompt_gen_image': 2000,
        'edit_prompt_negative_gen_image': 1000,
        'edit_prompt_gen_scenario_image': 2000,
        'edit_match_rate_roleset': 500,
        'edit_character_id': 50,
        'edit_voice_id': 50
    };

    for (const [fieldId, maxLength] of Object.entries(textFieldLimits)) {
        const field = document.getElementById(fieldId);
        if (field && field.value && field.value.length > maxLength) {
            showFieldError(field, `Cannot exceed ${maxLength} characters`);
            isValid = false;
        }
    }

    return isValid;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    field.parentNode.appendChild(feedback);
}

function saveCharacterChanges() {
    // Validate form first
    if (!validateCharacterForm()) {
        document.getElementById('characterSaveStatus').innerHTML =
            '<div class="alert alert-warning">Please fix the validation errors above.</div>';
        return;
    }

    const form = document.getElementById('characterEditForm');
    const formData = new FormData(form);
    const saveBtn = document.getElementById('saveCharacterBtn');
    const statusDiv = document.getElementById('characterSaveStatus');

    // Show loading state
    saveBtn.querySelector('.save-btn-text').classList.add('d-none');
    saveBtn.querySelector('.save-btn-loading').classList.remove('d-none');
    saveBtn.disabled = true;
    statusDiv.innerHTML = '<div class="alert alert-info">Saving changes...</div>';

    // Convert FormData to JSON for bulk update
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (key !== 'csrf_token') {
            data[key] = value || null;
        }
    }

    // Send bulk update request
    fetch(`/api/characters/{{ character.id }}/fields`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': formData.get('csrf_token')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            let message = 'All changes saved successfully!';
            if (result.mongodb_synced) {
                message += ' (Synced to MongoDB)';
            }
            statusDiv.innerHTML = `<div class="alert alert-success">${message}</div>`;

            // Close modal and refresh page after delay
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('characterEditModal')).hide();
                window.location.reload();
            }, 1500);
        } else {
            // Handle field-specific errors
            if (result.failed_fields && result.failed_fields.length > 0) {
                let errorHtml = '<div class="alert alert-danger"><strong>Some fields failed to save:</strong><ul class="mb-0 mt-2">';
                result.failed_fields.forEach(failure => {
                    errorHtml += `<li>${failure.field.replace('_', ' ')}: ${failure.error}</li>`;
                });
                errorHtml += '</ul></div>';
                statusDiv.innerHTML = errorHtml;
            } else {
                statusDiv.innerHTML = `<div class="alert alert-danger">Error: ${result.message || 'Failed to save changes'}</div>`;
            }
        }
    })
    .catch(error => {
        console.error('Error saving character:', error);
        statusDiv.innerHTML = '<div class="alert alert-danger">Error saving changes. Please try again.</div>';
    })
    .finally(() => {
        // Restore button state
        saveBtn.querySelector('.save-btn-text').classList.remove('d-none');
        saveBtn.querySelector('.save-btn-loading').classList.add('d-none');
        saveBtn.disabled = false;
    });
}

// Make functions globally available immediately
window.openCharacterEditModal = openCharacterEditModal;
window.populateCharacterEditForm = populateCharacterEditForm;
window.validateCharacterForm = validateCharacterForm;
window.showFieldError = showFieldError;
window.saveCharacterChanges = saveCharacterChanges;

function confirmDelete(characterName, deleteUrl) {
    document.getElementById('deleteCharacterName').textContent = characterName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

{% if qdrant_enabled %}
function filterQdrantImages(nsfw = null) {
    const container = document.getElementById('qdrant-images-container');
    const loading = document.getElementById('qdrant-loading');

    // Show loading
    container.classList.add('d-none');
    loading.classList.remove('d-none');

    // Build query parameters
    const params = new URLSearchParams();
    if (nsfw !== null) params.append('nsfw', nsfw);
    params.append('limit', '50');

    // Make request
    fetch(`{{ url_for('characters.qdrant_images', id=character.id) }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateQdrantImagesDisplay(data.images);
            } else {
                console.error('Error loading images:', data.error);
                container.innerHTML = `<div class="col-12"><div class="alert alert-danger">Error: ${data.error}</div></div>`;
            }
        })
        .catch(error => {
            console.error('Network error:', error);
            container.innerHTML = '<div class="col-12"><div class="alert alert-danger">Network error loading images</div></div>';
        })
        .finally(() => {
            loading.classList.add('d-none');
            container.classList.remove('d-none');
        });
}

function updateQdrantImagesDisplay(images) {
    const container = document.getElementById('qdrant-images-container');

    if (images.length === 0) {
        container.innerHTML = '<div class="col-12"><div class="text-center text-muted py-4"><i class="bi bi-images fs-1"></i><p class="mt-2">No images found with current filters.</p></div></div>';
        return;
    }

    container.innerHTML = images.map(image => `
        <div class="col-md-3 col-sm-4 col-6 mb-3">
            <div class="card h-100 qdrant-image-card">
                ${image.metadata.image_url ? `
                    <img src="${image.metadata.image_url}"
                         class="card-img-top"
                         style="height: 200px; object-fit: cover; cursor: pointer;"
                         onclick="showQdrantImageModal('${image.id}', '${image.metadata.image_url}', ${JSON.stringify(image.metadata).replace(/"/g, '&quot;')})"
                         alt="Generated Image">
                ` : `
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                         style="height: 200px;">
                        <i class="bi bi-image fs-1 text-muted"></i>
                    </div>
                `}
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        ${image.metadata.type ? `<span class="badge bg-info">${image.metadata.type}</span>` : ''}
                        ${image.metadata.type_nsfw ? '<span class="badge bg-danger">NSFW</span>' : '<span class="badge bg-success">SFW</span>'}
                    </div>
                    ${image.metadata.timestamp ? `<small class="text-muted">${image.metadata.timestamp}</small>` : ''}
                    ${image.text ? `<p class="card-text small text-truncate" title="${image.text}">${image.text.substring(0, 50)}${image.text.length > 50 ? '...' : ''}</p>` : ''}
                </div>
            </div>
        </div>
    `).join('');
}

let currentQdrantImageId = null;
let currentImageZoom = 1;

function showQdrantImageModal(imageId, imageUrl, metadata) {
    // Store current image ID for deletion
    currentQdrantImageId = imageId;

    // Reset zoom
    currentImageZoom = 1;

    // Set image
    const modalImage = document.getElementById('qdrantModalImage');
    modalImage.src = imageUrl;
    modalImage.style.transform = 'scale(1)';

    // Set up "Open in New Tab" button
    document.getElementById('openInNewTabBtn').onclick = () => window.open(imageUrl, '_blank');

    // Build enhanced details HTML
    const details = document.getElementById('qdrantModalDetails');
    details.innerHTML = `
        <div class="h-100 overflow-auto">
            <h6 class="border-bottom pb-2">Image Details</h6>
            <div class="mb-3">
                <p class="mb-1"><strong>ID:</strong></p>
                <code class="small">${imageId}</code>
            </div>

            ${metadata.character_id ? `
                <div class="mb-3">
                    <p class="mb-1"><strong>Character ID:</strong></p>
                    <code class="small">${metadata.character_id}</code>
                </div>
            ` : ''}

            <div class="mb-3">
                <p class="mb-1"><strong>Content Type:</strong></p>
                <div>
                    ${metadata.type ? `<span class="badge bg-info me-1">${metadata.type}</span>` : ''}
                    <span class="badge ${metadata.type_nsfw ? 'bg-danger' : 'bg-success'}">${metadata.type_nsfw ? 'NSFW' : 'SFW'}</span>
                </div>
            </div>

            ${metadata.scenario_mode !== undefined ? `
                <div class="mb-3">
                    <p class="mb-1"><strong>Scenario Mode:</strong></p>
                    <span class="badge ${metadata.scenario_mode ? 'bg-success' : 'bg-secondary'}">${metadata.scenario_mode ? 'Yes' : 'No'}</span>
                </div>
            ` : ''}

            ${metadata.timestamp ? `
                <div class="mb-3">
                    <p class="mb-1"><strong>Created:</strong></p>
                    <small class="text-muted">${metadata.timestamp}</small>
                </div>
            ` : ''}

            ${metadata.user_id ? `
                <div class="mb-3">
                    <p class="mb-1"><strong>User ID:</strong></p>
                    <code class="small">${metadata.user_id}</code>
                </div>
            ` : ''}

            ${metadata.session_id ? `
                <div class="mb-3">
                    <p class="mb-1"><strong>Session ID:</strong></p>
                    <code class="small">${metadata.session_id}</code>
                </div>
            ` : ''}

            ${metadata.prompt_image ? `
                <div class="mt-4">
                    <h6 class="border-bottom pb-2">Generation Prompt</h6>
                    <div class="bg-light p-3 rounded">
                        ${metadata.prompt_image.style ? `<div class="mb-2"><strong>Style:</strong> ${metadata.prompt_image.style}</div>` : ''}
                        ${metadata.prompt_image.emotion ? `<div class="mb-2"><strong>Emotion:</strong> ${metadata.prompt_image.emotion}</div>` : ''}
                        ${metadata.prompt_image.prompt ? `<div class="mb-2"><strong>Prompt:</strong><br><small class="text-muted">${metadata.prompt_image.prompt}</small></div>` : ''}
                        ${metadata.prompt_image.negative_prompt ? `<div class="mb-2"><strong>Negative:</strong><br><small class="text-muted">${metadata.prompt_image.negative_prompt}</small></div>` : ''}
                        ${metadata.prompt_image.image_size ? `<div class="mb-2"><strong>Size:</strong> ${metadata.prompt_image.image_size}</div>` : ''}
                        ${metadata.prompt_image.quality ? `<div class="mb-2"><strong>Quality:</strong> ${metadata.prompt_image.quality}</div>` : ''}
                        ${metadata.prompt_image.creativity ? `<div class="mb-2"><strong>Creativity:</strong> ${metadata.prompt_image.creativity}</div>` : ''}
                    </div>
                </div>
            ` : ''}
        </div>
    `;

    // Show modal
    new bootstrap.Modal(document.getElementById('qdrantImageModal')).show();
}

// Image zoom functions
function toggleImageZoom(img) {
    if (currentImageZoom === 1) {
        zoomImage(2);
    } else {
        resetImageZoom();
    }
}

function zoomImage(factor) {
    currentImageZoom *= factor;
    const img = document.getElementById('qdrantModalImage');
    img.style.transform = `scale(${currentImageZoom})`;
    img.style.cursor = currentImageZoom > 1 ? 'zoom-out' : 'zoom-in';
}

function resetImageZoom() {
    currentImageZoom = 1;
    const img = document.getElementById('qdrantModalImage');
    img.style.transform = 'scale(1)';
    img.style.cursor = 'zoom-in';
}

function confirmDeleteQdrantImage(imageId, characterId) {
    currentQdrantImageId = imageId;
    document.getElementById('confirmQdrantDeleteBtn').onclick = function() {
        deleteQdrantImage(imageId);
    };
    new bootstrap.Modal(document.getElementById('deleteQdrantImageModal')).show();
}

function deleteCurrentQdrantImage() {
    if (currentQdrantImageId) {
        confirmDeleteQdrantImage(currentQdrantImageId);
    }
}

function deleteQdrantImage(imageId) {
    // Show loading state
    const deleteBtn = document.getElementById('confirmQdrantDeleteBtn');
    const originalText = deleteBtn.innerHTML;
    deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Deleting...';
    deleteBtn.disabled = true;

    fetch(`/api/qdrant/images/${imageId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            // Success - close modals and refresh image grid
            bootstrap.Modal.getInstance(document.getElementById('deleteQdrantImageModal')).hide();
            bootstrap.Modal.getInstance(document.getElementById('qdrantImageModal'))?.hide();

            showMessage('success', 'Image deleted successfully from vector database');

            // Remove the image from the grid immediately
            const imageCards = document.querySelectorAll('.qdrant-image-card');
            imageCards.forEach(card => {
                const img = card.querySelector('img');
                if (img && img.onclick && img.onclick.toString().includes(imageId)) {
                    card.parentElement.remove();
                }
            });

            // Update image count if displayed
            const totalBadge = document.querySelector('.badge.bg-primary');
            if (totalBadge && totalBadge.textContent.includes('total')) {
                const currentCount = parseInt(totalBadge.textContent.match(/\d+/)[0]);
                totalBadge.textContent = `${currentCount - 1} total`;
            }
        } else {
            throw new Error(data.error || 'Failed to delete image');
        }
    })
    .catch(error => {
        console.error('Error deleting image:', error);
        showMessage('error', error.message || 'Failed to delete image');
    })
    .finally(() => {
        // Restore button state
        deleteBtn.innerHTML = originalText;
        deleteBtn.disabled = false;
    });
}

// MongoDB Fields Modal Functions
function openMongoDBFieldsModal() {
    new bootstrap.Modal(document.getElementById('mongoDBFieldsModal')).show();
}

function saveAllMongoDBFields() {
    const form = document.getElementById('mongoDBFieldsForm');
    const formData = new FormData(form);
    const saveBtn = document.getElementById('saveMongoFieldsBtn');
    const statusSpan = document.getElementById('mongoFieldsSaveStatus');

    // Show loading state
    saveBtn.querySelector('.save-btn-text').classList.add('d-none');
    saveBtn.querySelector('.save-btn-loading').classList.remove('d-none');
    saveBtn.disabled = true;
    statusSpan.textContent = 'Saving changes...';
    statusSpan.className = 'text-info';

    // Convert FormData to JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (key !== 'csrf_token') {
            data[key] = value || null;
        }
    }

    // Save each field individually
    const fieldPromises = Object.entries(data).map(([field, value]) => {
        return fetch(`/api/characters/{{ character.id }}/field`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': formData.get('csrf_token')
            },
            body: JSON.stringify({ field, value })
        }).then(response => response.json());
    });

    Promise.all(fieldPromises)
        .then(results => {
            const failures = results.filter(result => !result.success);

            if (failures.length === 0) {
                statusSpan.textContent = 'All changes saved successfully!';
                statusSpan.className = 'text-success';

                // Close modal after delay
                setTimeout(() => {
                    bootstrap.Modal.getInstance(document.getElementById('mongoDBFieldsModal')).hide();
                    // Refresh page to show updated data
                    window.location.reload();
                }, 1500);
            } else {
                statusSpan.textContent = `${failures.length} fields failed to save. Check console for details.`;
                statusSpan.className = 'text-danger';
                console.error('Failed field updates:', failures);
            }
        })
        .catch(error => {
            console.error('Error saving MongoDB fields:', error);
            statusSpan.textContent = 'Error saving changes. Please try again.';
            statusSpan.className = 'text-danger';
        })
        .finally(() => {
            // Restore button state
            saveBtn.querySelector('.save-btn-text').classList.remove('d-none');
            saveBtn.querySelector('.save-btn-loading').classList.add('d-none');
            saveBtn.disabled = false;
        });
}
{% endif %}

// Inline Field Editing Functions
function editField(fieldName, currentValue, fieldType, options = null) {
    const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
    const editBtn = fieldElement.querySelector('.edit-btn');

    // Hide edit button
    editBtn.style.display = 'none';

    // Create input element based on field type
    let inputElement;
    if (fieldType === 'select' && options) {
        inputElement = document.createElement('select');
        inputElement.className = 'form-select form-select-sm d-inline-block w-auto';

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option || 'Not set';
            if (option === currentValue) {
                optionElement.selected = true;
            }
            inputElement.appendChild(optionElement);
        });
    } else if (fieldType === 'textarea') {
        inputElement = document.createElement('textarea');
        inputElement.className = 'form-control form-control-sm';
        inputElement.rows = 3;
        inputElement.value = currentValue;
    } else {
        inputElement = document.createElement('input');
        inputElement.type = fieldType === 'number' ? 'number' : 'text';
        inputElement.className = 'form-control form-control-sm d-inline-block w-auto';
        inputElement.value = currentValue;
    }

    // Create action buttons
    const saveBtn = document.createElement('button');
    saveBtn.className = 'btn btn-sm btn-success ms-2';
    saveBtn.innerHTML = '<i class="bi bi-check"></i>';
    saveBtn.onclick = () => saveField(fieldName, inputElement.value, fieldElement, editBtn);

    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'btn btn-sm btn-secondary ms-1';
    cancelBtn.innerHTML = '<i class="bi bi-x"></i>';
    cancelBtn.onclick = () => cancelEdit(fieldName, currentValue, fieldElement, editBtn);

    // Replace content
    const valueSpan = fieldElement.querySelector('span, code, em') || fieldElement.childNodes[0];
    const originalContent = valueSpan.innerHTML;

    // Store original content for cancel
    fieldElement.dataset.originalContent = originalContent;

    // Replace with input and buttons
    valueSpan.innerHTML = '';
    valueSpan.appendChild(inputElement);
    valueSpan.appendChild(saveBtn);
    valueSpan.appendChild(cancelBtn);

    // Focus input
    inputElement.focus();

    // Handle Enter key
    inputElement.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && fieldType !== 'textarea') {
            saveField(fieldName, inputElement.value, fieldElement, editBtn);
        }
    });

    // Handle Escape key
    inputElement.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            cancelEdit(fieldName, currentValue, fieldElement, editBtn);
        }
    });
}

function saveField(fieldName, newValue, fieldElement, editBtn) {
    // Show loading state
    const valueSpan = fieldElement.querySelector('span, code, em') || fieldElement.childNodes[0];
    valueSpan.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Saving...';

    // Make API request
    fetch(`/api/characters/{{ character.id }}/field`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            field: fieldName,
            value: newValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update display with new value
            updateFieldDisplay(fieldName, newValue, fieldElement, editBtn);

            // Show success message
            showMessage('success', data.message || 'Field updated successfully');
        } else {
            throw new Error(data.error || 'Failed to update field');
        }
    })
    .catch(error => {
        console.error('Error updating field:', error);
        showMessage('error', error.message || 'Failed to update field');

        // Restore original content
        const originalContent = fieldElement.dataset.originalContent;
        valueSpan.innerHTML = originalContent;
        editBtn.style.display = 'inline-block';
    });
}

function cancelEdit(fieldName, originalValue, fieldElement, editBtn) {
    // Restore original content
    const valueSpan = fieldElement.querySelector('span, code, em') || fieldElement.childNodes[0];
    const originalContent = fieldElement.dataset.originalContent;
    valueSpan.innerHTML = originalContent;
    editBtn.style.display = 'inline-block';
}

function updateFieldDisplay(fieldName, newValue, fieldElement, editBtn) {
    const valueSpan = fieldElement.querySelector('span, code, em') || fieldElement.childNodes[0];

    // Format display based on field type
    let displayValue;
    if (!newValue || newValue.trim() === '') {
        displayValue = '<em class="text-muted">Not set</em>';
    } else if (fieldName === 'character_id') {
        displayValue = `<code>${newValue}</code>`;
    } else {
        displayValue = newValue;
    }

    valueSpan.innerHTML = displayValue;
    editBtn.style.display = 'inline-block';
}

function showMessage(type, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of page
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}



// Additional validation for the simplified version at the top
// (Extended validation is in the main function above)
</script>
{% endblock %}
