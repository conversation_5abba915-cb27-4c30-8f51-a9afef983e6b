{% extends "base.html" %}

{% block title %}{{ character.name }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ character.name }}</h5>
                <div>
                    <span class="badge bg-warning">MongoDB Only</span>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    This character exists in MongoDB but hasn't been synced to the local database yet.
                    Some features may be limited.
                </div>
                
                {% if character.bio %}
                <h6>Bio</h6>
                <p class="text-muted">{{ character.bio }}</p>
                {% endif %}
                
                {% if character.personality_roleplay %}
                <h6>Personality</h6>
                <p class="text-muted">{{ character.personality_roleplay }}</p>
                {% endif %}
                
                {% if character.scenario_information %}
                <h6>Scenario</h6>
                <p class="text-muted">{{ character.scenario_information }}</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-person"></i> Character Info</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">ID:</dt>
                    <dd class="col-sm-7">{{ character.character_id }}</dd>
                    
                    {% if character.age %}
                    <dt class="col-sm-5">Age:</dt>
                    <dd class="col-sm-7">{{ character.age }}</dd>
                    {% endif %}
                    
                    {% if character.occupation %}
                    <dt class="col-sm-5">Occupation:</dt>
                    <dd class="col-sm-7">{{ character.occupation }}</dd>
                    {% endif %}
                    
                    {% if character.country %}
                    <dt class="col-sm-5">Country:</dt>
                    <dd class="col-sm-7">{{ character.country }}</dd>
                    {% endif %}
                    
                    {% if character.gender %}
                    <dt class="col-sm-5">Gender:</dt>
                    <dd class="col-sm-7">{{ character.gender }}</dd>
                    {% endif %}
                    
                    {% if character.types %}
                    <dt class="col-sm-5">Type:</dt>
                    <dd class="col-sm-7">{{ character.types }}</dd>
                    {% endif %}
                </dl>
                
                {% if character.match_rate_roleset %}
                <h6>Tags</h6>
                <div class="mb-2">
                    {% for tag in character.match_rate_roleset %}
                    <span class="badge bg-secondary me-1" style="font-size: 0.7rem;">
                        {{ tag }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Limited Features</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    This character is only available in MongoDB. To access full features:
                </p>
                <ul class="small text-muted">
                    <li>Local image uploads</li>
                    <li>Character editing</li>
                    <li>Tag management</li>
                    <li>Full image gallery</li>
                </ul>
                <p class="text-muted small">
                    Contact an administrator to sync this character to the local database.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <a href="{{ url_for('characters.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Characters
            </a>
        </div>
    </div>
</div>
{% endblock %}
