{% extends "base.html" %}

{% block title %}Edit {{ character.name }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-pencil"></i> Edit {{ character.name }}</h1>
            <div>
                <a href="{{ url_for('characters.view', id=character.id) }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Character
                </a>
                {% if can_delete_character(character) %}
                <button type="button" class="btn btn-outline-danger" 
                        onclick="confirmDelete('{{ character.name }}', '{{ url_for('characters.delete', id=character.id) }}')">
                    <i class="bi bi-trash"></i> Delete
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Character Information</h5>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs mb-4" id="characterTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                                <i class="bi bi-person"></i> Basic Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                                <i class="bi bi-palette"></i> Appearance
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="personality-tab" data-bs-toggle="tab" data-bs-target="#personality" type="button" role="tab">
                                <i class="bi bi-chat-heart"></i> Personality
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="prompts-tab" data-bs-toggle="tab" data-bs-target="#prompts" type="button" role="tab">
                                <i class="bi bi-magic"></i> AI Prompts
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                                <i class="bi bi-gear"></i> Advanced
                            </button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="characterTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.name.label(class="form-label") }}
                                        {{ form.name(class="form-control") }}
                                        {% if form.name.errors %}
                                            <div class="text-danger">
                                                {% for error in form.name.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.character_id.label(class="form-label") }}
                                        <small class="text-muted">(Unique identifier)</small>
                                        {{ form.character_id(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.age.label(class="form-label") }}
                                        {{ form.age(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.gender.label(class="form-label") }}
                                        {{ form.gender(class="form-control") }}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.occupation.label(class="form-label") }}
                                        {{ form.occupation(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.country.label(class="form-label") }}
                                        {{ form.country(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.hobbies.label(class="form-label") }}
                                        {{ form.hobbies(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.types.label(class="form-label") }}
                                        {{ form.types(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.bio.label(class="form-label") }}
                                <small class="text-muted">(Short bio/profile description)</small>
                                {{ form.bio(class="form-control", rows="3") }}
                            </div>
                        </div>
                        
                        <!-- Appearance Tab -->
                        <div class="tab-pane fade" id="appearance" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.hair_color.label(class="form-label") }}
                                        {{ form.hair_color(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.hair_style.label(class="form-label") }}
                                        {{ form.hair_style(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.eye_color.label(class="form-label") }}
                                        {{ form.eye_color(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.skin_color.label(class="form-label") }}
                                        {{ form.skin_color(class="form-control") }}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.eye_type.label(class="form-label") }}
                                        {{ form.eye_type(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.face_detail.label(class="form-label") }}
                                        {{ form.face_detail(class="form-control", rows="3") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.body_detail.label(class="form-label") }}
                                        {{ form.body_detail(class="form-control", rows="3") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Personality Tab -->
                        <div class="tab-pane fade" id="personality" role="tabpanel">
                            <div class="mb-3">
                                {{ form.personality_roleplay.label(class="form-label") }}
                                <small class="text-muted">(Character's personality and roleplay style)</small>
                                {{ form.personality_roleplay(class="form-control", rows="4") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.style_roleplay.label(class="form-label") }}
                                <small class="text-muted">(How the character communicates)</small>
                                {{ form.style_roleplay(class="form-control", rows="3") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.nsfw_style.label(class="form-label") }}
                                <small class="text-muted">(Optional - NSFW communication style)</small>
                                {{ form.nsfw_style(class="form-control", rows="3") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.scenario_information.label(class="form-label") }}
                                <small class="text-muted">(Default scenario/setting)</small>
                                {{ form.scenario_information(class="form-control", rows="4") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.match_rate_roleset.label(class="form-label") }}
                                <small class="text-muted">(Comma-separated tags for matching)</small>
                                {{ form.match_rate_roleset(class="form-control") }}
                            </div>
                        </div>
                        
                        <!-- AI Prompts Tab -->
                        <div class="tab-pane fade" id="prompts" role="tabpanel">
                            <div class="mb-3">
                                {{ form.prompt_gen_image.label(class="form-label") }}
                                <small class="text-muted">(Positive prompt for image generation)</small>
                                {{ form.prompt_gen_image(class="form-control", rows="4") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.prompt_negative_gen_image.label(class="form-label") }}
                                <small class="text-muted">(Negative prompt for image generation)</small>
                                {{ form.prompt_negative_gen_image(class="form-control", rows="3") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.prompt_gen_scenario_image.label(class="form-label") }}
                                <small class="text-muted">(Prompt for scenario/background images)</small>
                                {{ form.prompt_gen_scenario_image(class="form-control", rows="3") }}
                            </div>
                        </div>
                        
                        <!-- Advanced Tab -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.timezone.label(class="form-label") }}
                                        <small class="text-muted">(UTC offset)</small>
                                        {{ form.timezone(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.voice_id.label(class="form-label") }}
                                        <small class="text-muted">(Voice ID for TTS)</small>
                                        {{ form.voice_id(class="form-control") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.template_image.label(class="form-label") }}
                                        {{ form.template_image(class="form-control") }}
                                        {% if character.template_image %}
                                        <small class="text-muted">Current: {{ character.template_image }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.tags.label(class="form-label") }}
                                        <small class="text-muted">(Hold Ctrl/Cmd to select multiple)</small>
                                        {{ form.tags(class="form-select", multiple=true, size="5") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.description.label(class="form-label") }}
                                        <small class="text-muted">(Legacy description field)</small>
                                        {{ form.description(class="form-control", rows="3") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.attributes.label(class="form-label") }}
                                        <small class="text-muted">(Legacy JSON attributes)</small>
                                        {{ form.attributes(class="form-control", rows="3") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ url_for('characters.view', id=character.id) }}" class="btn btn-secondary me-2">Cancel</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete "<span id="deleteCharacterName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone and will also delete all associated images.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(characterName, deleteUrl) {
    document.getElementById('deleteCharacterName').textContent = characterName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
