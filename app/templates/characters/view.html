{% extends "base.html" %}

{% block title %}{{ character.name }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-person"></i> {{ character.name }}</h1>
            <div>
                <a href="{{ url_for('characters.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back
                </a>
                {% if can_edit_character(character) %}
                <a href="{{ url_for('characters.edit', id=character.id) }}" class="btn btn-outline-primary">
                    <i class="bi bi-pencil"></i> Edit
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Character Details</h5>
            </div>
            {% if character.template_image %}
            <img src="{{ url_for('images.serve_image', filename=character.template_image) }}" 
                 class="card-img-top" style="height: 300px; object-fit: cover;" 
                 alt="{{ character.name }}">
            {% else %}
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                 style="height: 300px;">
                <i class="bi bi-person fs-1 text-muted"></i>
            </div>
            {% endif %}
            
            <div class="card-body">
                {% if character.description %}
                <h6>Description</h6>
                <p class="text-muted">{{ character.description }}</p>
                {% endif %}
                
                {% if character.tags %}
                <h6>Tags</h6>
                <div class="mb-3">
                    {% for tag in character.tags %}
                    <span class="badge me-1" style="background-color: {{ tag.color }};">
                        {{ tag.name }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if character.attributes %}
                <h6>Attributes</h6>
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0"><code>{{ character.attributes }}</code></pre>
                </div>
                {% endif %}
                
                <hr>
                <small class="text-muted">
                    <i class="bi bi-person"></i> Created by {{ character.creator.username }}<br>
                    <i class="bi bi-calendar"></i> {{ character.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                    {% if character.updated_at != character.created_at %}
                    <i class="bi bi-pencil"></i> Updated {{ character.updated_at.strftime('%Y-%m-%d %H:%M') }}<br>
                    {% endif %}
                    <i class="bi bi-images"></i> {{ character.generated_images.count() }} generated images
                </small>
            </div>
        </div>
        
        {% if current_user.is_authenticated %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <a href="{{ url_for('images.upload') }}?character_id={{ character.id }}" 
                   class="btn btn-success btn-sm w-100 mb-2">
                    <i class="bi bi-upload"></i> Upload Image
                </a>
                <a href="{{ url_for('images.bulk_upload') }}?character_id={{ character.id }}" 
                   class="btn btn-info btn-sm w-100">
                    <i class="bi bi-cloud-upload"></i> Bulk Upload
                </a>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Generated Images ({{ character.generated_images.count() }})</h5>
                {% if current_user.is_authenticated %}
                <a href="{{ url_for('images.upload') }}?character_id={{ character.id }}" 
                   class="btn btn-success btn-sm">
                    <i class="bi bi-plus"></i> Add Image
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if images %}
                <div class="row">
                    {% for image in images %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="card h-100">
                            <img src="{{ url_for('images.serve_image', filename=image.thumbnail) }}" 
                                 class="card-img-top" style="height: 150px; object-fit: cover;" 
                                 alt="{{ image.original_filename }}">
                            <div class="card-body p-2">
                                {% if image.description %}
                                <small class="text-muted d-block text-truncate">{{ image.description }}</small>
                                {% endif %}
                                <small class="text-muted">
                                    {{ image.created_at.strftime('%m/%d/%Y') }}
                                </small>
                            </div>
                            <div class="card-footer p-2">
                                <a href="{{ url_for('images.view', id=image.id) }}" 
                                   class="btn btn-sm btn-outline-primary w-100">View</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination for images -->
                {% if pagination.pages > 1 %}
                <nav aria-label="Images pagination">
                    <ul class="pagination pagination-sm justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('characters.view', id=character.id, page=pagination.prev_num) }}">
                                Previous
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('characters.view', id=character.id, page=page_num) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('characters.view', id=character.id, page=pagination.next_num) }}">
                                Next
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-images fs-1 text-muted"></i>
                    <h5 class="text-muted mt-3">No images yet</h5>
                    <p class="text-muted">Upload some generated images for this character.</p>
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('images.upload') }}?character_id={{ character.id }}" 
                       class="btn btn-success">
                        <i class="bi bi-upload"></i> Upload First Image
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
