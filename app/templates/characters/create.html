{% extends "base.html" %}

{% block title %}Create Character - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-plus-circle"></i> Create Character</h1>
            <a href="{{ url_for('characters.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Characters
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Character Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="4", placeholder="Describe your character...") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.attributes.label(class="form-label") }}
                        <small class="text-muted">(Optional JSON format for custom attributes)</small>
                        {{ form.attributes(class="form-control", rows="6", placeholder='{\n  "age": "25",\n  "height": "5\'8\"",\n  "hair_color": "brown",\n  "eye_color": "blue"\n}') }}
                        {% if form.attributes.errors %}
                            <div class="text-danger">
                                {% for error in form.attributes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.tags.label(class="form-label") }}
                        <small class="text-muted">(Hold Ctrl/Cmd to select multiple)</small>
                        {{ form.tags(class="form-select", multiple=True, size="5") }}
                        {% if form.tags.errors %}
                            <div class="text-danger">
                                {% for error in form.tags.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.template_image.label(class="form-label") }}
                        <div class="upload-area">
                            {{ form.template_image(class="form-control", style="display: none;") }}
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mt-2 mb-0">Click to select or drag and drop an image</p>
                            <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP (Max 16MB)</small>
                        </div>
                        {% if form.template_image.errors %}
                            <div class="text-danger mt-2">
                                {% for error in form.template_image.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('characters.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Name:</strong> Choose a unique, memorable name for your character.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Description:</strong> Provide details about personality, background, and appearance.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Attributes:</strong> Use JSON format for structured data like age, height, etc.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Tags:</strong> Select relevant tags to help categorize your character.
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Template Image:</strong> Upload a reference image that represents your character.
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-palette"></i> Available Tags</h6>
            </div>
            <div class="card-body">
                {% if form.tags.choices %}
                <div class="d-flex flex-wrap gap-1">
                    {% for tag_id, tag_name in form.tags.choices %}
                    <span class="badge bg-secondary">{{ tag_name }}</span>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No tags available. Contact an administrator to create tags.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // JSON validation for attributes field
    const attributesField = document.querySelector('#attributes');
    if (attributesField) {
        attributesField.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value && value !== '') {
                try {
                    JSON.parse(value);
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    }
});
</script>
{% endblock %}
