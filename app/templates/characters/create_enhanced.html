{% extends "base.html" %}

{% block title %}Create Character - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-plus-circle"></i> Create Character</h1>
            <a href="{{ url_for('characters.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Characters
            </a>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Character Information</h5>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs mb-4" id="characterTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                                <i class="bi bi-person"></i> Basic Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                                <i class="bi bi-palette"></i> Appearance
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="personality-tab" data-bs-toggle="tab" data-bs-target="#personality" type="button" role="tab">
                                <i class="bi bi-chat-heart"></i> Personality
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="prompts-tab" data-bs-toggle="tab" data-bs-target="#prompts" type="button" role="tab">
                                <i class="bi bi-magic"></i> AI Prompts
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                                <i class="bi bi-gear"></i> Advanced
                            </button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="characterTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.name.label(class="form-label") }}
                                        {{ form.name(class="form-control", placeholder="Enter character name") }}
                                        {% if form.name.errors %}
                                            <div class="text-danger">
                                                {% for error in form.name.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.character_id.label(class="form-label") }}
                                        <small class="text-muted">(Unique identifier, e.g., "ayumi")</small>
                                        {{ form.character_id(class="form-control", placeholder="unique_character_id") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.age.label(class="form-label") }}
                                        {{ form.age(class="form-control", placeholder="25") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.gender.label(class="form-label") }}
                                        {{ form.gender(class="form-control", placeholder="Female/Male/Other") }}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.occupation.label(class="form-label") }}
                                        {{ form.occupation(class="form-control", placeholder="High School Math Teacher") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.country.label(class="form-label") }}
                                        {{ form.country(class="form-control", placeholder="Osaka, Japan") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.hobbies.label(class="form-label") }}
                                        {{ form.hobbies(class="form-control", placeholder="Puzzles, Reading") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.types.label(class="form-label") }}
                                        {{ form.types(class="form-control", placeholder="Anime") }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.bio.label(class="form-label") }}
                                <small class="text-muted">(Short bio/profile description)</small>
                                {{ form.bio(class="form-control", rows="3", placeholder="👩🏻‍🏫 Ayumi, 29. Osaka math teacher...") }}
                            </div>
                        </div>
                        
                        <!-- Appearance Tab -->
                        <div class="tab-pane fade" id="appearance" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.hair_color.label(class="form-label") }}
                                        {{ form.hair_color(class="form-control", placeholder="Jet Black") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.hair_style.label(class="form-label") }}
                                        {{ form.hair_style(class="form-control", placeholder="Long") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.eye_color.label(class="form-label") }}
                                        {{ form.eye_color(class="form-control", placeholder="Icy Blue") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.skin_color.label(class="form-label") }}
                                        {{ form.skin_color(class="form-control", placeholder="Pale Porcelain") }}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.eye_type.label(class="form-label") }}
                                        {{ form.eye_type(class="form-control", placeholder="Sharp, with long wispy lashes") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.face_detail.label(class="form-label") }}
                                        {{ form.face_detail(class="form-control", rows="3", placeholder="Straight sharp nose, thin dark eyebrows, smirking lips...") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.body_detail.label(class="form-label") }}
                                        {{ form.body_detail(class="form-control", rows="3", placeholder="Tall and slender figure, large breasts") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Personality Tab -->
                        <div class="tab-pane fade" id="personality" role="tabpanel">
                            <div class="mb-3">
                                {{ form.personality_roleplay.label(class="form-label") }}
                                <small class="text-muted">(Character's personality and roleplay style)</small>
                                {{ form.personality_roleplay(class="form-control", rows="4", placeholder="Ayumi often presents a cool, confident exterior...") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.style_roleplay.label(class="form-label") }}
                                <small class="text-muted">(How the character communicates)</small>
                                {{ form.style_roleplay(class="form-control", rows="3", placeholder="Communicates with sharp clarity and precision...") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.nsfw_style.label(class="form-label") }}
                                <small class="text-muted">(Optional - NSFW communication style)</small>
                                {{ form.nsfw_style(class="form-control", rows="3", placeholder="Approaches intimate topics with...") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.scenario_information.label(class="form-label") }}
                                <small class="text-muted">(Default scenario/setting)</small>
                                {{ form.scenario_information(class="form-control", rows="4", placeholder="The chalkboard glows under the last rays...") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.match_rate_roleset.label(class="form-label") }}
                                <small class="text-muted">(Comma-separated tags for matching)</small>
                                {{ form.match_rate_roleset(class="form-control", placeholder="Japanese Girl, Teasing, Busty, School Uniform") }}
                            </div>
                        </div>
                        
                        <!-- AI Prompts Tab -->
                        <div class="tab-pane fade" id="prompts" role="tabpanel">
                            <div class="mb-3">
                                {{ form.prompt_gen_image.label(class="form-label") }}
                                <small class="text-muted">(Positive prompt for image generation)</small>
                                {{ form.prompt_gen_image(class="form-control", rows="4", placeholder="best quality, thigh-up shot, beautiful woman...") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.prompt_negative_gen_image.label(class="form-label") }}
                                <small class="text-muted">(Negative prompt for image generation)</small>
                                {{ form.prompt_negative_gen_image(class="form-control", rows="3", placeholder="extra arms, extra legs, extra fingers, bad anatomy...") }}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.prompt_gen_scenario_image.label(class="form-label") }}
                                <small class="text-muted">(Prompt for scenario/background images)</small>
                                {{ form.prompt_gen_scenario_image(class="form-control", rows="3", placeholder="classroom, sunset light, chalkboard glow...") }}
                            </div>
                        </div>
                        
                        <!-- Advanced Tab -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.timezone.label(class="form-label") }}
                                        <small class="text-muted">(UTC offset, e.g., 9 for JST)</small>
                                        {{ form.timezone(class="form-control", placeholder="9") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.voice_id.label(class="form-label") }}
                                        <small class="text-muted">(Voice ID for TTS)</small>
                                        {{ form.voice_id(class="form-control", placeholder="af_jessica") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.template_image.label(class="form-label") }}
                                        {{ form.template_image(class="form-control") }}
                                        {% if form.template_image.errors %}
                                            <div class="text-danger">
                                                {% for error in form.template_image.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.tags.label(class="form-label") }}
                                        <small class="text-muted">(Hold Ctrl/Cmd to select multiple)</small>
                                        {{ form.tags(class="form-select", multiple=true, size="5") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.description.label(class="form-label") }}
                                        <small class="text-muted">(Legacy description field)</small>
                                        {{ form.description(class="form-control", rows="3", placeholder="Legacy description...") }}
                                    </div>
                                    
                                    <div class="mb-3">
                                        {{ form.attributes.label(class="form-label") }}
                                        <small class="text-muted">(Legacy JSON attributes)</small>
                                        {{ form.attributes(class="form-control", rows="3", placeholder='{"custom": "attributes"}') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ url_for('characters.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}
