{% extends "base.html" %}

{% block title %}Edit {{ character.name }} - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-pencil-square"></i> Edit Character: {{ character.name }}</h1>
            <div>
                <a href="{{ url_for('characters.view', id=character.id) }}" class="btn btn-outline-secondary">
                    <i class="bi bi-eye"></i> View Character
                </a>
                <a href="{{ url_for('characters.index') }}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Characters
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Character Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="4", placeholder="Describe your character...") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.attributes.label(class="form-label") }}
                        <small class="text-muted">(Optional JSON format for custom attributes)</small>
                        {{ form.attributes(class="form-control", rows="6") }}
                        {% if form.attributes.errors %}
                            <div class="text-danger">
                                {% for error in form.attributes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.tags.label(class="form-label") }}
                        <small class="text-muted">(Hold Ctrl/Cmd to select multiple)</small>
                        {{ form.tags(class="form-select", multiple=True, size="5") }}
                        {% if form.tags.errors %}
                            <div class="text-danger">
                                {% for error in form.tags.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.template_image.label(class="form-label") }}
                        <small class="text-muted">(Leave empty to keep current image)</small>
                        
                        {% if character.template_image %}
                        <div class="current-image mb-3">
                            <label class="form-label">Current Image:</label>
                            <div class="d-flex align-items-center">
                                <img src="{{ url_for('images.serve_image', filename=character.template_image) }}" 
                                     class="img-thumbnail me-3" style="width: 100px; height: 100px; object-fit: cover;" 
                                     alt="{{ character.name }}">
                                <div>
                                    <p class="mb-1"><strong>{{ character.template_image }}</strong></p>
                                    <small class="text-muted">Upload a new image to replace this one</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="upload-area">
                            {{ form.template_image(class="form-control", style="display: none;") }}
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mt-2 mb-0">Click to select or drag and drop a new image</p>
                            <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP (Max 16MB)</small>
                        </div>
                        {% if form.template_image.errors %}
                            <div class="text-danger mt-2">
                                {% for error in form.template_image.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('characters.view', id=character.id) }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary", value="Update Character") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Character Details</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Created:</dt>
                    <dd class="col-sm-8">{{ character.created_at.strftime('%Y-%m-%d %H:%M') }}</dd>
                    
                    <dt class="col-sm-4">Updated:</dt>
                    <dd class="col-sm-8">{{ character.updated_at.strftime('%Y-%m-%d %H:%M') }}</dd>
                    
                    <dt class="col-sm-4">Creator:</dt>
                    <dd class="col-sm-8">{{ character.creator.username }}</dd>
                    
                    <dt class="col-sm-4">Images:</dt>
                    <dd class="col-sm-8">{{ character.generated_images.count() }} generated images</dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-palette"></i> Current Tags</h6>
            </div>
            <div class="card-body">
                {% if character.tags %}
                <div class="d-flex flex-wrap gap-2 mb-3">
                    {% for tag in character.tags %}
                    <span class="badge" style="background-color: {{ tag.color }};">{{ tag.name }}</span>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No tags assigned</p>
                {% endif %}
                
                <hr>
                <h6>Available Tags:</h6>
                {% if form.tags.choices %}
                <div class="d-flex flex-wrap gap-1">
                    {% for tag_id, tag_name in form.tags.choices %}
                    <span class="badge bg-secondary">{{ tag_name }}</span>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No tags available</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Danger Zone</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">Permanently delete this character and all associated images.</p>
                <button type="button" class="btn btn-danger btn-sm w-100" 
                        onclick="confirmDelete('{{ character.name }}', '{{ url_for('characters.delete', id=character.id) }}')">
                    <i class="bi bi-trash"></i> Delete Character
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the character "<span id="deleteCharacterName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone and will also delete all associated images.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // JSON validation for attributes field
    const attributesField = document.querySelector('#attributes');
    if (attributesField) {
        attributesField.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value && value !== '') {
                try {
                    JSON.parse(value);
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    }
});

function confirmDelete(characterName, deleteUrl) {
    document.getElementById('deleteCharacterName').textContent = characterName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
