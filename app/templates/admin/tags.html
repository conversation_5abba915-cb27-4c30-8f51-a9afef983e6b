{% extends "base.html" %}

{% block title %}Manage Tags - Admin - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-tags"></i> Manage Tags</h1>
            <div>
                <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Admin
                </a>
                <a href="{{ url_for('admin.create_tag') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create Tag
                </a>
            </div>
        </div>
    </div>
</div>

{% if tags %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">All Tags ({{ tags|length }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Name</th>
                                <th>Color</th>
                                <th>Characters</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tag in tags %}
                            <tr>
                                <td>
                                    <span class="badge" style="background-color: {{ tag.color }};">
                                        {{ tag.name }}
                                    </span>
                                </td>
                                <td>
                                    <strong>{{ tag.name }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="color-preview me-2" 
                                             style="width: 20px; height: 20px; background-color: {{ tag.color }}; border-radius: 4px; border: 1px solid #dee2e6;"></div>
                                        <code>{{ tag.color }}</code>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ tag.characters|length }}</span>
                                    {% if tag.characters %}
                                    <div class="mt-1">
                                        {% for character in tag.characters[:3] %}
                                        <small class="text-muted">{{ character.name }}{% if not loop.last %}, {% endif %}</small>
                                        {% endfor %}
                                        {% if tag.characters|length > 3 %}
                                        <small class="text-muted">... +{{ tag.characters|length - 3 }} more</small>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ tag.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('admin.edit_tag', id=tag.id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        {% if not tag.characters %}
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="confirmDelete('{{ tag.name }}', '{{ url_for('admin.delete_tag', id=tag.id) }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                disabled title="Cannot delete tag that is in use">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tag Usage Statistics -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-bar-chart"></i> Tag Usage Statistics</h6>
            </div>
            <div class="card-body">
                {% set sorted_tags = tags|sort(attribute='characters')|reverse %}
                {% for tag in sorted_tags[:10] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center">
                        <span class="badge me-2" style="background-color: {{ tag.color }};">{{ tag.name }}</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress me-2" style="width: 100px; height: 8px;">
                            <div class="progress-bar" style="width: {{ (tag.characters|length / (sorted_tags[0].characters|length if sorted_tags[0].characters else 1)) * 100 }}%; background-color: {{ tag.color }};"></div>
                        </div>
                        <small class="text-muted">{{ tag.characters|length }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-palette"></i> Color Distribution</h6>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    {% for tag in tags %}
                    <div class="color-swatch" 
                         style="width: 30px; height: 30px; background-color: {{ tag.color }}; border-radius: 6px; border: 2px solid #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                         title="{{ tag.name }} ({{ tag.color }})"></div>
                    {% endfor %}
                </div>
                
                {% if tags|length > 20 %}
                <div class="mt-3">
                    <small class="text-muted">Showing first 20 colors. Total: {{ tags|length }} tags.</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="bi bi-tags fs-1 text-muted"></i>
            <h3 class="text-muted mt-3">No tags created yet</h3>
            <p class="text-muted">Create your first tag to start organizing characters.</p>
            <a href="{{ url_for('admin.create_tag') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Create First Tag
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the tag "<span id="deleteTagName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(tagName, deleteUrl) {
    document.getElementById('deleteTagName').textContent = tagName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
