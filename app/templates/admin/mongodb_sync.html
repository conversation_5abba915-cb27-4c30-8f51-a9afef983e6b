{% extends "base.html" %}

{% block title %}MongoDB Synchronization - Character CMS{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-cloud-arrow-up-down me-2"></i>
                    MongoDB Synchronization
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">Admin</a></li>
                        <li class="breadcrumb-item active">MongoDB Sync</li>
                    </ol>
                </nav>
            </div>

            <!-- MongoDB Status Card -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                MongoDB Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="mongodb-status">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span>Checking MongoDB status...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Synchronization Controls -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-cloud-download me-2"></i>
                                Import from MongoDB
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                Import characters from MongoDB to SQLite database. This will add new characters and optionally update existing ones.
                            </p>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="forceUpdateImport">
                                <label class="form-check-label" for="forceUpdateImport">
                                    Force update existing characters
                                </label>
                            </div>
                            <button type="button" class="btn btn-primary" id="syncFromMongoDB">
                                <i class="bi bi-cloud-download me-2"></i>
                                Import from MongoDB
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-cloud-upload me-2"></i>
                                Export to MongoDB
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                Export characters from SQLite to MongoDB. This will sync all local characters to the MongoDB database.
                            </p>
                            <button type="button" class="btn btn-success" id="syncToMongoDB">
                                <i class="bi bi-cloud-upload me-2"></i>
                                Export to MongoDB
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sync Results -->
            <div class="row mb-4" id="syncResults" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-check-circle me-2"></i>
                                Synchronization Results
                            </h5>
                        </div>
                        <div class="card-body" id="syncResultsContent">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- MongoDB Characters Preview -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-database me-2"></i>
                                MongoDB Characters
                            </h5>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="refreshMongoCharacters">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="mongoCharacters">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span>Loading MongoDB characters...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MongoDB Field Mapping Modal -->
<div class="modal fade" id="fieldMappingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">MongoDB Field Mapping</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="fieldMappingContent">
                    <!-- Field mapping will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load MongoDB status on page load
    loadMongoDBStatus();
    loadMongoCharacters();

    // Event listeners
    document.getElementById('syncFromMongoDB').addEventListener('click', syncFromMongoDB);
    document.getElementById('syncToMongoDB').addEventListener('click', syncToMongoDB);
    document.getElementById('refreshMongoCharacters').addEventListener('click', loadMongoCharacters);

    function loadMongoDBStatus() {
        fetch('/api/mongodb/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('mongodb-status');
                if (data.success) {
                    const status = data.status;
                    statusDiv.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-${status.enabled ? 'check-circle text-success' : 'x-circle text-danger'} me-2"></i>
                                    <strong>Enabled:</strong> <span class="ms-2">${status.enabled ? 'Yes' : 'No'}</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-${status.connected ? 'wifi text-success' : 'wifi-off text-danger'} me-2"></i>
                                    <strong>Connected:</strong> <span class="ms-2">${status.connected ? 'Yes' : 'No'}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-database me-2"></i>
                                    <strong>Database:</strong> <span class="ms-2">${status.database}</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-collection me-2"></i>
                                    <strong>Collection:</strong> <span class="ms-2">${status.collection}</span>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Error loading MongoDB status: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('mongodb-status').innerHTML = `
                    <div class="alert alert-danger mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Error: ${error.message}
                    </div>
                `;
            });
    }

    function loadMongoCharacters() {
        const container = document.getElementById('mongoCharacters');
        container.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span>Loading MongoDB characters...</span>
            </div>
        `;

        fetch('/api/mongodb/characters')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.characters.length === 0) {
                        container.innerHTML = `
                            <div class="alert alert-info mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                No characters found in MongoDB.
                            </div>
                        `;
                    } else {
                        let html = `
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Character ID</th>
                                            <th>Name</th>
                                            <th>Age</th>
                                            <th>Occupation</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;
                        
                        data.characters.forEach(char => {
                            html += `
                                <tr>
                                    <td><code>${char.character_id || 'N/A'}</code></td>
                                    <td>${char.name || 'N/A'}</td>
                                    <td>${char.age || 'N/A'}</td>
                                    <td>${char.occupation || 'N/A'}</td>
                                    <td>${char.types || 'N/A'}</td>
                                </tr>
                            `;
                        });
                        
                        html += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">Total: ${data.count} characters</small>
                            </div>
                        `;
                        
                        container.innerHTML = html;
                    }
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Error loading characters: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                container.innerHTML = `
                    <div class="alert alert-danger mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Error: ${error.message}
                    </div>
                `;
            });
    }

    function syncFromMongoDB() {
        const button = document.getElementById('syncFromMongoDB');
        const originalText = button.innerHTML;
        const forceUpdate = document.getElementById('forceUpdateImport').checked;
        
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Importing...';
        
        fetch('/api/mongodb/sync/from-mongodb', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ force_update: forceUpdate })
        })
        .then(response => response.json())
        .then(data => {
            showSyncResults(data, 'Import from MongoDB');
            if (data.success) {
                loadMongoCharacters();
            }
        })
        .catch(error => {
            showSyncResults({ success: false, error: error.message }, 'Import from MongoDB');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    function syncToMongoDB() {
        const button = document.getElementById('syncToMongoDB');
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Exporting...';
        
        fetch('/api/mongodb/sync/to-mongodb', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            showSyncResults(data, 'Export to MongoDB');
            if (data.success) {
                loadMongoCharacters();
            }
        })
        .catch(error => {
            showSyncResults({ success: false, error: error.message }, 'Export to MongoDB');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    function showSyncResults(data, operation) {
        const resultsDiv = document.getElementById('syncResults');
        const contentDiv = document.getElementById('syncResultsContent');
        
        if (data.success) {
            const stats = data.stats;
            contentDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle me-2"></i>${operation} completed successfully!</h6>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success">${stats.imported || stats.exported || 0}</div>
                                <small class="text-muted">${operation.includes('Import') ? 'Imported' : 'Exported'}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-info">${stats.updated || 0}</div>
                                <small class="text-muted">Updated</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-warning">${stats.skipped || 0}</div>
                                <small class="text-muted">Skipped</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-danger">${stats.errors || 0}</div>
                                <small class="text-muted">Errors</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>${operation} failed!</h6>
                    <p class="mb-0">${data.error}</p>
                </div>
            `;
        }
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    }
});

// Add spinning animation for loading buttons
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
