{% extends "base.html" %}

{% block title %}Admin Dashboard - Character CMS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-gear"></i> Admin Dashboard</h1>
            <div class="badge bg-primary">Admin Panel</div>
        </div>
    </div>
</div>

<!-- Admin Statistics -->
<div class="row mb-5">
    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_users }}</h3>
                        <p class="card-text mb-0">Total Users</p>
                        <small class="opacity-75">{{ stats.admin_users }} admins</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('admin.users') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>Manage Users</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_characters }}</h3>
                        <p class="card-text mb-0">Characters</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-badge" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('characters.index') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>View Characters</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_images }}</h3>
                        <p class="card-text mb-0">Generated Images</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-images" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('images.index') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>View Images</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_tags }}</h3>
                        <p class="card-text mb-0">Tags</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-tags" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('admin.tags') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>Manage Tags</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-3">Quick Actions</h3>
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ url_for('admin.users') }}" class="btn btn-primary">
                <i class="bi bi-people"></i> Manage Users
            </a>
            <a href="{{ url_for('admin.tags') }}" class="btn btn-success">
                <i class="bi bi-tags"></i> Manage Tags
            </a>
            <a href="{{ url_for('admin.create_tag') }}" class="btn btn-info">
                <i class="bi bi-plus-circle"></i> Create Tag
            </a>
            <a href="{{ url_for('admin.system_info') }}" class="btn btn-warning">
                <i class="bi bi-info-circle"></i> System Info
            </a>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-people"></i> Recent Users</h6>
            </div>
            <div class="card-body">
                {% if recent_users %}
                <div class="list-group list-group-flush">
                    {% for user in recent_users %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <div class="fw-bold">{{ user.username }}</div>
                            <small class="text-muted">{{ user.email }}</small>
                        </div>
                        <div>
                            {% if user.is_admin %}
                            <span class="badge bg-danger">Admin</span>
                            {% endif %}
                            <small class="text-muted">{{ user.created_at.strftime('%m/%d') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No users yet.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary btn-sm w-100">
                    View All Users
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-person-badge"></i> Recent Characters</h6>
            </div>
            <div class="card-body">
                {% if recent_characters %}
                <div class="list-group list-group-flush">
                    {% for character in recent_characters %}
                    <div class="list-group-item d-flex align-items-center px-0">
                        {% if character.thumbnail %}
                        <img src="{{ url_for('images.serve_image', filename=character.thumbnail) }}" 
                             class="rounded me-2" style="width: 30px; height: 30px; object-fit: cover;" 
                             alt="{{ character.name }}">
                        {% else %}
                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                             style="width: 30px; height: 30px;">
                            <i class="bi bi-person text-muted"></i>
                        </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ character.name }}</div>
                            <small class="text-muted">by {{ character.creator.username }}</small>
                        </div>
                        <small class="text-muted">{{ character.created_at.strftime('%m/%d') }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No characters yet.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('characters.index') }}" class="btn btn-outline-success btn-sm w-100">
                    View All Characters
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-images"></i> Recent Images</h6>
            </div>
            <div class="card-body">
                {% if recent_images %}
                <div class="list-group list-group-flush">
                    {% for image in recent_images %}
                    <div class="list-group-item d-flex align-items-center px-0">
                        <img src="{{ url_for('images.serve_image', filename=image.thumbnail) }}" 
                             class="rounded me-2" style="width: 30px; height: 30px; object-fit: cover;" 
                             alt="{{ image.original_filename }}">
                        <div class="flex-grow-1">
                            {% set character = characters_data.get(image.character_id) %}
                            <div class="fw-bold">{{ character.name if character else image.character_id }}</div>
                            <small class="text-muted">by {{ image.uploader.username }}</small>
                        </div>
                        <small class="text-muted">{{ image.created_at.strftime('%m/%d') }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">No images yet.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('images.index') }}" class="btn btn-outline-info btn-sm w-100">
                    View All Images
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
