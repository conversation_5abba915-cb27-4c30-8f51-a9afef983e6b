{% extends "base.html" %}

{% block title %}Dashboard - Character CMS{% endblock %}

{% block content %}
{% if current_user.is_authenticated %}
<!-- Authenticated User Dashboard -->
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i> Dashboard
        </h1>
    </div>
</div>
{% else %}
<!-- Unauthenticated User Landing Page -->
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <h1 class="display-4 mb-4">
                <i class="bi bi-palette"></i> Character CMS
            </h1>
            <p class="lead mb-4">
                Manage your characters and their generated images with ease
            </p>
            <p class="text-muted mb-5">
                Create, organize, and showcase your character collection with our powerful content management system.
            </p>

            <div class="d-flex justify-content-center gap-3 mb-5">
                <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg">
                    <i class="bi bi-box-arrow-in-right"></i> Login
                </a>
                <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-person-plus"></i> Register
                </a>
            </div>

            <!-- Features Section -->
            <div class="row mt-5">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="bi bi-people fs-1 text-primary mb-3"></i>
                            <h5>Character Management</h5>
                            <p class="text-muted">Create and organize your character profiles with detailed information and attributes.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="bi bi-images fs-1 text-success mb-3"></i>
                            <h5>Image Gallery</h5>
                            <p class="text-muted">Upload and manage generated images for your characters with advanced organization tools.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="bi bi-tags fs-1 text-info mb-3"></i>
                            <h5>Smart Tagging</h5>
                            <p class="text-muted">Organize your content with customizable tags and powerful search functionality.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if current_user.is_authenticated %}
<!-- Enhanced Statistics Cards -->
<div class="row mb-5">
    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_characters }}</h3>
                        <p class="card-text mb-0">Characters</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('characters.index') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>View all</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_images }}</h3>
                        <p class="card-text mb-0">Generated Images</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-images" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('images.index') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>View all</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_users }}</h3>
                        <p class="card-text mb-0">Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-check" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            {% if user_permissions.can_access_admin %}
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('admin.users') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>Manage users</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-2">{{ stats.total_tags }}</h3>
                        <p class="card-text mb-0">Tags</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-tags" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
            {% if user_permissions.can_access_admin %}
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('admin.tags') }}" class="text-white text-decoration-none d-flex align-items-center">
                    <span>Manage tags</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Quick Actions -->
{% if user_permissions.is_authenticated %}
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-3">Quick Actions</h3>
        <div class="d-flex gap-2 flex-wrap">
            {% if user_permissions.can_create_character %}
            <a href="{{ url_for('characters.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Create Character
            </a>
            {% endif %}
            {% if user_permissions.can_upload_image %}
            <a href="{{ url_for('images.upload') }}" class="btn btn-success">
                <i class="bi bi-upload"></i> Upload Image
            </a>
            {% endif %}
            {% if user_permissions.can_bulk_upload %}
            <a href="{{ url_for('images.bulk_upload') }}" class="btn btn-info">
                <i class="bi bi-cloud-upload"></i> Bulk Upload
            </a>
            {% endif %}
            {% if user_permissions.can_access_admin %}
            <a href="{{ url_for('admin.index') }}" class="btn btn-warning">
                <i class="bi bi-gear"></i> Admin Panel
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Characters -->
<div class="row mb-5">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3>Recent Characters</h3>
            <a href="{{ url_for('characters.index') }}" class="btn btn-outline-primary btn-sm">
                View All <i class="bi bi-arrow-right"></i>
            </a>
        </div>
        
        {% if recent_characters %}
        <div class="row">
            {% for character in recent_characters %}
            <div class="col-md-4 col-lg-2 mb-3">
                <div class="card h-100">
                    {% if character.thumbnail %}
                    <img src="{{ url_for('images.serve_image', filename=character.thumbnail) }}" 
                         class="card-img-top" style="height: 150px; object-fit: cover;" 
                         alt="{{ character.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 150px;">
                        <i class="bi bi-person fs-1 text-muted"></i>
                    </div>
                    {% endif %}
                    <div class="card-body p-2">
                        <h6 class="card-title mb-1">{{ character.name }}</h6>
                        <small class="text-muted">
                            {{ character.generated_images.count() }} images
                        </small>
                    </div>
                    <div class="card-footer p-2">
                        <a href="{{ url_for('characters.view', id=character.id) }}" 
                           class="btn btn-sm btn-outline-primary w-100">View</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people fs-1 text-muted"></i>
            <p class="text-muted mt-2">No characters yet.</p>
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('characters.create') }}" class="btn btn-primary">
                Create Your First Character
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Recent Images -->
<div class="row mb-5">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3>Recent Images</h3>
            <a href="{{ url_for('images.index') }}" class="btn btn-outline-success btn-sm">
                View All <i class="bi bi-arrow-right"></i>
            </a>
        </div>
        
        {% if recent_images %}
        <div class="row">
            {% for image in recent_images %}
            <div class="col-md-3 col-lg-2 mb-3">
                <div class="card h-100">
                    <img src="{{ url_for('images.serve_image', filename=image.thumbnail) }}" 
                         class="card-img-top" style="height: 120px; object-fit: cover;" 
                         alt="{{ image.original_filename }}">
                    <div class="card-body p-2">
                        <small class="text-muted d-block">{{ image.character.name }}</small>
                        <small class="text-muted">{{ image.created_at.strftime('%m/%d') }}</small>
                    </div>
                    <div class="card-footer p-2">
                        <a href="{{ url_for('images.view', id=image.id) }}" 
                           class="btn btn-sm btn-outline-success w-100">View</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-images fs-1 text-muted"></i>
            <p class="text-muted mt-2">No images yet.</p>
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('images.upload') }}" class="btn btn-success">
                Upload Your First Image
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Popular Tags -->
{% if popular_tags %}
<div class="row">
    <div class="col-12">
        <h3 class="mb-3">Popular Tags</h3>
        <div class="d-flex flex-wrap gap-2">
            {% for tag, count in popular_tags %}
            <span class="badge fs-6" style="background-color: {{ tag.color }};">
                {{ tag.name }} ({{ count }})
            </span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endif %}
{% endblock %}
