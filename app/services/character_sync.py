#!/usr/bin/env python3
"""
Character Synchronization Service
Handles bidirectional sync between MongoDB and SQLite
"""

import json
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from flask import current_app

from app.models import Character, User
from app import db
from .mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class CharacterSyncService:
    """Service for synchronizing characters between MongoDB and SQLite"""
    
    def __init__(self):
        self.mongodb_service = mongodb_service
    
    def sync_from_mongodb(self, force_update: bool = False) -> Dict[str, int]:
        """
        Sync characters from MongoDB to SQLite
        
        Args:
            force_update: If True, update existing characters even if they exist
            
        Returns:
            Dict with sync statistics: {'imported': int, 'updated': int, 'skipped': int, 'errors': int}
        """
        if not self.mongodb_service.is_enabled():
            logger.warning("MongoDB not enabled. Cannot sync from MongoDB.")
            return {'imported': 0, 'updated': 0, 'skipped': 0, 'errors': 0}
        
        stats = {'imported': 0, 'updated': 0, 'skipped': 0, 'errors': 0}
        
        try:
            # Get all characters from MongoDB
            mongo_characters = self.mongodb_service.get_all_characters()
            logger.info(f"Found {len(mongo_characters)} characters in MongoDB")
            
            # Get default admin user for character ownership
            admin_user = User.query.filter_by(is_admin=True).first()
            if not admin_user:
                logger.error("No admin user found. Cannot import characters.")
                return stats
            
            for mongo_char in mongo_characters:
                try:
                    result = self._import_character_from_mongo(mongo_char, admin_user.id, force_update)
                    stats[result] += 1
                except Exception as e:
                    logger.error(f"Error importing character {mongo_char.get('character_id', 'unknown')}: {e}")
                    stats['errors'] += 1
            
            # Commit all changes
            db.session.commit()
            logger.info(f"MongoDB sync completed: {stats}")
            
        except Exception as e:
            logger.error(f"Error during MongoDB sync: {e}")
            db.session.rollback()
            stats['errors'] += len(mongo_characters) if 'mongo_characters' in locals() else 1
        
        return stats
    
    def sync_to_mongodb(self, character_ids: Optional[List[int]] = None) -> Dict[str, int]:
        """
        Sync characters from SQLite to MongoDB
        
        Args:
            character_ids: List of character IDs to sync. If None, sync all characters.
            
        Returns:
            Dict with sync statistics: {'exported': int, 'updated': int, 'skipped': int, 'errors': int}
        """
        if not self.mongodb_service.is_enabled():
            logger.warning("MongoDB not enabled. Cannot sync to MongoDB.")
            return {'exported': 0, 'updated': 0, 'skipped': 0, 'errors': 0}
        
        stats = {'exported': 0, 'updated': 0, 'skipped': 0, 'errors': 0}
        
        try:
            # Get characters to sync
            if character_ids:
                characters = Character.query.filter(Character.id.in_(character_ids)).all()
            else:
                characters = Character.query.all()
            
            logger.info(f"Syncing {len(characters)} characters to MongoDB")
            
            for character in characters:
                try:
                    result = self._export_character_to_mongo(character)
                    stats[result] += 1
                except Exception as e:
                    logger.error(f"Error exporting character {character.character_id}: {e}")
                    stats['errors'] += 1
            
            logger.info(f"SQLite to MongoDB sync completed: {stats}")
            
        except Exception as e:
            logger.error(f"Error during SQLite to MongoDB sync: {e}")
            stats['errors'] += len(characters) if 'characters' in locals() else 1
        
        return stats
    
    def _import_character_from_mongo(self, mongo_char: Dict, admin_user_id: int, force_update: bool) -> str:
        """Import a single character from MongoDB to SQLite"""
        character_id = mongo_char.get('character_id')
        
        if not character_id:
            logger.warning("Character missing character_id, skipping")
            return 'skipped'
        
        # Check if character already exists
        existing_char = Character.query.filter_by(character_id=character_id).first()
        
        if existing_char and not force_update:
            logger.debug(f"Character {character_id} already exists, skipping")
            return 'skipped'
        
        try:
            if existing_char:
                # Update existing character
                self._update_character_from_mongo(existing_char, mongo_char)
                logger.info(f"Updated character {character_id} from MongoDB")
                return 'updated'
            else:
                # Create new character
                new_char = self._create_character_from_mongo(mongo_char, admin_user_id)
                db.session.add(new_char)
                logger.info(f"Imported new character {character_id} from MongoDB")
                return 'imported'
                
        except Exception as e:
            logger.error(f"Error processing character {character_id}: {e}")
            raise
    
    def _create_character_from_mongo(self, mongo_char: Dict, admin_user_id: int) -> Character:
        """Create a new Character instance from MongoDB data"""
        # Map MongoDB fields to SQLite Character model
        character_data = self._map_mongo_to_sqlite(mongo_char)
        character_data['created_by'] = admin_user_id
        
        return Character(**character_data)
    
    def _update_character_from_mongo(self, character: Character, mongo_char: Dict):
        """Update existing Character instance with MongoDB data"""
        character_data = self._map_mongo_to_sqlite(mongo_char)
        
        # Update character fields
        for field, value in character_data.items():
            if hasattr(character, field):
                setattr(character, field, value)
        
        character.updated_at = datetime.utcnow()
    
    def _map_mongo_to_sqlite(self, mongo_char: Dict) -> Dict:
        """Map MongoDB character data to SQLite Character model fields"""
        field_mapping = self.mongodb_service.get_mongodb_field_mapping()
        character_data = {}
        
        for mongo_field, sqlite_field in field_mapping.items():
            value = mongo_char.get(mongo_field)
            
            if value is not None:
                # Special handling for match_rate_roleset
                if mongo_field == 'match_rate_roleset':
                    if isinstance(value, list):
                        character_data[sqlite_field] = json.dumps(value)
                    else:
                        character_data[sqlite_field] = value
                else:
                    character_data[sqlite_field] = value
        
        return character_data
    
    def _export_character_to_mongo(self, character: Character) -> str:
        """Export a SQLite character to MongoDB"""
        character_id = character.character_id
        
        if not character_id:
            logger.warning(f"Character {character.id} missing character_id, skipping export")
            return 'skipped'
        
        # Check if character exists in MongoDB
        existing_mongo_char = self.mongodb_service.get_character_by_id(character_id)
        
        # Prepare character data for MongoDB
        character_data = self._map_sqlite_to_mongo(character)
        
        if existing_mongo_char:
            # Update existing character in MongoDB
            success = self.mongodb_service.update_character(character_id, character_data)
            if success:
                logger.info(f"Updated character '{character_id}' in MongoDB")
                return 'updated'
            else:
                logger.error(f"Failed to update character '{character_id}' in MongoDB")
                return 'errors'
        else:
            # Insert new character into MongoDB
            result = self.mongodb_service.insert_character(character_data)
            if result:
                logger.info(f"Exported character '{character_id}' to MongoDB with ID: {result}")
                return 'exported'
            else:
                logger.error(f"Failed to export character '{character_id}' to MongoDB")
                return 'errors'
    
    def _map_sqlite_to_mongo(self, character: Character) -> Dict:
        """Map SQLite Character model to MongoDB document"""
        field_mapping = self.mongodb_service.get_mongodb_field_mapping()
        mongo_data = {}

        logger.debug(f"Mapping character {character.character_id} from SQLite to MongoDB")

        for mongo_field, sqlite_field in field_mapping.items():
            if hasattr(character, sqlite_field):
                value = getattr(character, sqlite_field)

                if value is not None:
                    # Special handling for match_rate_roleset
                    if sqlite_field == 'match_rate_roleset':
                        if isinstance(value, str):
                            try:
                                mongo_data[mongo_field] = json.loads(value)
                            except (json.JSONDecodeError, TypeError):
                                mongo_data[mongo_field] = []
                        else:
                            mongo_data[mongo_field] = value
                    else:
                        mongo_data[mongo_field] = value

                    logger.debug(f"Mapped {sqlite_field}='{value}' -> {mongo_field}='{mongo_data[mongo_field]}'")

        logger.debug(f"Final mapped data for {character.character_id}: {mongo_data}")
        return mongo_data
    
    def update_character_field(self, character_id: str, field_name: str, field_value: any) -> bool:
        """
        Update a specific field for a character in both SQLite and MongoDB
        
        Args:
            character_id: The character's unique identifier
            field_name: The field to update
            field_value: The new value for the field
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            # Update SQLite
            character = Character.query.filter_by(character_id=character_id).first()
            if not character:
                logger.error(f"Character {character_id} not found in SQLite")
                return False
            
            if hasattr(character, field_name):
                setattr(character, field_name, field_value)
                character.updated_at = datetime.utcnow()
                db.session.commit()
                logger.info(f"Updated {field_name} for character {character_id} in SQLite")
            else:
                logger.error(f"Field {field_name} not found in Character model")
                return False
            
            # Update MongoDB if enabled
            if self.mongodb_service.is_enabled():
                mongo_field_mapping = self.mongodb_service.get_mongodb_field_mapping()
                
                # Find the MongoDB field name
                mongo_field = None
                for mongo_f, sqlite_f in mongo_field_mapping.items():
                    if sqlite_f == field_name:
                        mongo_field = mongo_f
                        break
                
                if mongo_field:
                    # Prepare value for MongoDB
                    mongo_value = field_value
                    if field_name == 'match_rate_roleset' and isinstance(field_value, str):
                        try:
                            mongo_value = json.loads(field_value)
                        except (json.JSONDecodeError, TypeError):
                            mongo_value = []
                    
                    success = self.mongodb_service.update_character(
                        character_id, 
                        {mongo_field: mongo_value}
                    )
                    
                    if success:
                        logger.info(f"Updated {mongo_field} for character {character_id} in MongoDB")
                    else:
                        logger.warning(f"Failed to update {mongo_field} for character {character_id} in MongoDB")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating field {field_name} for character {character_id}: {e}")
            db.session.rollback()
            return False

    def sync_character_to_mongodb(self, character: Character) -> bool:
        """
        Sync a single character to MongoDB

        Args:
            character: The Character instance to sync

        Returns:
            bool: True if sync was successful, False otherwise
        """
        if not self.mongodb_service.is_enabled():
            logger.warning("MongoDB not enabled. Cannot sync character to MongoDB.")
            return False

        if not character.character_id:
            logger.warning(f"Character {character.id} missing character_id, cannot sync to MongoDB")
            return False

        try:
            # Export character to MongoDB
            result = self._export_character_to_mongo(character)

            if result in ['exported', 'updated']:
                logger.info(f"Successfully synced character {character.character_id} to MongoDB")
                return True
            else:
                logger.error(f"Failed to sync character {character.character_id} to MongoDB: {result}")
                return False

        except Exception as e:
            logger.error(f"Error syncing character {character.character_id} to MongoDB: {e}")
            return False

# Global character sync service instance
character_sync_service = CharacterSyncService()
