#!/usr/bin/env python3
"""
Qdrant Vector Database Service
Service for querying images from Qdrant vector database
"""

import os
import logging
from typing import List, Dict, Optional, Any
from flask import current_app

logger = logging.getLogger(__name__)

class QdrantService:
    """Service for interacting with Qdrant vector database"""
    
    def __init__(self):
        self.client = None
        self.collection_name = None
        self.enabled = False
        
    def init_app(self, app):
        """Initialize Qdrant service with Flask app"""
        try:
            # Get configuration from app config
            self.qdrant_url = app.config.get('QDRANT_URL')
            self.qdrant_api_key = app.config.get('QDRANT_API_KEY')
            self.collection_name = app.config.get('QDRANT_COLLECTION', 'partnr-chat-image-dev')
            self.enabled = app.config.get('QDRANT_ENABLED', False)

            # Only work with real Qdrant connections
            if self.enabled and self.qdrant_url:
                try:
                    self._connect()
                    logger.info("Qdrant service initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Qdrant: {e}")
                    self.enabled = False
                    app.config['QDRANT_ENABLED'] = False
            else:
                logger.info("Qdrant not configured. Image search will be disabled.")
                
        except Exception as e:
            logger.error(f"Error initializing Qdrant service: {e}")
            self.enabled = False
    
    def _connect(self):
        """Connect to Qdrant database"""
        try:
            # Try to import qdrant_client
            from qdrant_client import QdrantClient
            from qdrant_client.http import models
            
            # Create client
            if self.qdrant_api_key:
                self.client = QdrantClient(
                    url=self.qdrant_url,
                    api_key=self.qdrant_api_key
                )
            else:
                self.client = QdrantClient(url=self.qdrant_url)
            
            # Test connection by getting collection info
            try:
                collection_info = self.client.get_collection(self.collection_name)
                logger.info(f"Connected to Qdrant collection: {self.collection_name}")
                logger.info(f"Collection points count: {collection_info.points_count}")
                return True
            except Exception as e:
                logger.warning(f"Collection {self.collection_name} not found or inaccessible: {e}")
                # Collection might not exist yet, but connection is working
                return True
                
        except ImportError:
            logger.error("qdrant-client not installed. Install with: pip install qdrant-client")
            raise
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {e}")
            raise
    
    def is_enabled(self) -> bool:
        """Check if Qdrant service is enabled and connected"""
        return self.enabled and self.client is not None
    
    def test_connection(self) -> bool:
        """Test Qdrant connection"""
        if not self.is_enabled():
            return False
        
        try:
            # Try to get collection info
            self.client.get_collection(self.collection_name)
            return True
        except Exception as e:
            logger.error(f"Qdrant connection test failed: {e}")
            return False
    
    def get_images_by_character_id(self, character_id: str, limit: int = 50, 
                                  filters: Optional[Dict] = None) -> List[Dict]:
        """
        Get images for a specific character from Qdrant
        
        Args:
            character_id: Character ID to search for
            limit: Maximum number of images to return
            filters: Additional filters (type, type_nsfw, scenario_mode, etc.)
            
        Returns:
            List of image records with metadata
        """
        if not self.is_enabled():
            logger.warning("Qdrant not enabled. Cannot retrieve images.")
            return []
        
        try:
            from qdrant_client.http import models

            logger.info(f"Searching for images with character_id: '{character_id}' in collection: '{self.collection_name}'")

            # Build filter conditions - character_id is nested under metadata
            filter_conditions = [
                models.FieldCondition(
                    key="metadata.character_id",
                    match=models.MatchValue(value=character_id)
                )
            ]
            
            # Add additional filters if provided - all nested under metadata
            if filters:
                if 'type' in filters and filters['type']:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="metadata.type",
                            match=models.MatchValue(value=filters['type'])
                        )
                    )

                if 'type_nsfw' in filters:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="metadata.type_nsfw",
                            match=models.MatchValue(value=filters['type_nsfw'])
                        )
                    )

                if 'scenario_mode' in filters:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="metadata.scenario_mode",
                            match=models.MatchValue(value=filters['scenario_mode'])
                        )
                    )
            
            # Create filter
            search_filter = models.Filter(
                must=filter_conditions
            )
            
            # Search for points
            search_result = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=search_filter,
                limit=limit,
                with_payload=True,
                with_vectors=False
            )
            
            # Extract and format results with enhanced data structure
            images = []
            for point in search_result[0]:  # search_result is (points, next_page_offset)
                if point.payload:
                    # Extract metadata from nested structure
                    nested_metadata = point.payload.get('metadata', {})

                    # Validate required fields in nested metadata
                    if 'character_id' not in nested_metadata:
                        logger.warning(f"Point {point.id} missing character_id in nested metadata")
                        continue

                    # Ensure image_file exists for URL construction
                    if 'image_file' not in nested_metadata or not nested_metadata['image_file']:
                        logger.warning(f"Point {point.id} missing or empty image_file in nested metadata")
                        # Skip images without valid file references
                        continue

                    # Construct full image URL from CDN base URL and image_file
                    cdn_base_url = current_app.config.get('CDN_BASE_URL', 'https://cdn.mirailabs.co/partnr/ai-generated')
                    image_url = f"{cdn_base_url}/{nested_metadata['image_file']}"

                    # Add the constructed URL to metadata for backward compatibility
                    enhanced_metadata = nested_metadata.copy()
                    enhanced_metadata['image_url'] = image_url

                    image_data = {
                        'id': str(point.id),
                        'text': point.payload.get('text', ''),
                        'metadata': enhanced_metadata  # Use enhanced metadata with constructed URL
                    }
                    images.append(image_data)

            logger.info(f"Retrieved {len(images)} valid images for character '{character_id}' from Qdrant")

            # If no images found, provide detailed debugging information
            if len(images) == 0:
                logger.warning(f"No images found for character_id '{character_id}'. Running diagnostics...")
                self._debug_character_search(character_id)

            return images

        except Exception as e:
            logger.error(f"Error retrieving images for character {character_id}: {e}")
            return []
    
    def search_images_by_text(self, query_text: str, character_id: Optional[str] = None, 
                             limit: int = 20) -> List[Dict]:
        """
        Search images by text description using vector similarity
        
        Args:
            query_text: Text to search for
            character_id: Optional character ID filter
            limit: Maximum number of results
            
        Returns:
            List of similar images with scores
        """
        if not self.is_enabled():
            logger.warning("Qdrant not enabled. Cannot search images.")
            return []
        
        try:
            from qdrant_client.http import models
            
            # For text search, we would need embeddings
            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Generate embeddings for the query_text
            # 2. Use client.search() with the embedding vector
            
            logger.warning("Text-based vector search not implemented yet. Use get_images_by_character_id instead.")
            
            # Fallback to metadata search
            if character_id:
                return self.get_images_by_character_id(character_id, limit)
            
            return []
            
        except Exception as e:
            logger.error(f"Error searching images by text '{query_text}': {e}")
            return []
    
    def get_image_metadata(self, image_id: str) -> Optional[Dict]:
        """
        Get metadata for a specific image
        
        Args:
            image_id: Image ID to retrieve
            
        Returns:
            Image metadata or None if not found
        """
        if not self.is_enabled():
            return None
        
        try:
            # Retrieve specific point
            points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=[image_id],
                with_payload=True,
                with_vectors=False
            )
            
            if points and len(points) > 0:
                point = points[0]
                # Extract nested metadata structure
                nested_metadata = point.payload.get('metadata', {})

                # Construct image URL if image_file exists
                if 'image_file' in nested_metadata and nested_metadata['image_file']:
                    cdn_base_url = current_app.config.get('CDN_BASE_URL', 'https://cdn.mirailabs.co/partnr/ai-generated')
                    enhanced_metadata = nested_metadata.copy()
                    enhanced_metadata['image_url'] = f"{cdn_base_url}/{nested_metadata['image_file']}"
                else:
                    enhanced_metadata = nested_metadata

                return {
                    'id': str(point.id),
                    'text': point.payload.get('text', ''),
                    'metadata': enhanced_metadata  # Use enhanced metadata with constructed URL
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving image metadata for {image_id}: {e}")
            return None
    
    def get_collection_stats(self) -> Dict:
        """Get collection statistics"""
        if not self.is_enabled():
            return {
                'enabled': False,
                'connected': False,
                'collection_name': self.collection_name,
                'points_count': 0,
                'error': 'Qdrant not enabled'
            }
        
        try:
            collection_info = self.client.get_collection(self.collection_name)
            
            return {
                'enabled': True,
                'connected': True,
                'collection_name': self.collection_name,
                'points_count': collection_info.points_count,
                'vectors_count': collection_info.vectors_count,
                'status': collection_info.status
            }
            
        except Exception as e:
            return {
                'enabled': True,
                'connected': False,
                'collection_name': self.collection_name,
                'points_count': 0,
                'error': str(e)
            }
    


    def get_character_image_count(self, character_id: str) -> int:
        """Get count of images for a specific character"""
        if not self.is_enabled():
            return 0
        
        try:
            from qdrant_client.http import models
            
            # Count points for character - character_id is nested under metadata
            filter_condition = models.Filter(
                must=[
                    models.FieldCondition(
                        key="metadata.character_id",
                        match=models.MatchValue(value=character_id)
                    )
                ]
            )
            
            count_result = self.client.count(
                collection_name=self.collection_name,
                count_filter=filter_condition
            )
            
            return count_result.count

        except Exception as e:
            logger.error(f"Error counting images for character {character_id}: {e}")
            return 0

    def _debug_character_search(self, character_id: str):
        """Debug method to help identify character ID matching issues"""
        try:
            # Get a sample of points to see what character IDs exist
            sample_result = self.client.scroll(
                collection_name=self.collection_name,
                limit=20,
                with_payload=True,
                with_vectors=False
            )

            sample_character_ids = set()
            total_points = len(sample_result[0])

            for point in sample_result[0]:
                if point.payload and 'metadata' in point.payload:
                    nested_metadata = point.payload['metadata']
                    if 'character_id' in nested_metadata:
                        sample_character_ids.add(nested_metadata['character_id'])

            logger.info(f"Debug: Found {total_points} sample points in collection")
            logger.info(f"Debug: Sample character IDs found: {sorted(list(sample_character_ids))}")
            logger.info(f"Debug: Searched for character_id: '{character_id}'")

            # Check for case sensitivity issues
            case_matches = [cid for cid in sample_character_ids if cid.lower() == character_id.lower()]
            if case_matches:
                logger.warning(f"Debug: Found case-insensitive matches: {case_matches}")

            # Check for partial matches
            partial_matches = [cid for cid in sample_character_ids if character_id in cid or cid in character_id]
            if partial_matches:
                logger.info(f"Debug: Found partial matches: {partial_matches}")

        except Exception as debug_e:
            logger.error(f"Error in debug character search: {debug_e}")

    def get_all_character_ids(self) -> List[str]:
        """Get all unique character IDs from the Qdrant collection"""
        if not self.is_enabled():
            return []

        try:
            # Scroll through all points to get character IDs
            character_ids = set()
            offset = None

            while True:
                result = self.client.scroll(
                    collection_name=self.collection_name,
                    limit=100,
                    offset=offset,
                    with_payload=True,
                    with_vectors=False
                )

                points, next_offset = result

                for point in points:
                    if point.payload and 'metadata' in point.payload:
                        nested_metadata = point.payload['metadata']
                        if 'character_id' in nested_metadata:
                            character_ids.add(nested_metadata['character_id'])

                if next_offset is None or len(points) == 0:
                    break

                offset = next_offset

            return sorted(list(character_ids))

        except Exception as e:
            logger.error(f"Error getting all character IDs: {e}")
            return []

    def delete_image(self, image_id: str) -> bool:
        """Delete an image from Qdrant by its ID"""
        if not self.is_enabled():
            logger.warning("Qdrant not enabled. Cannot delete image.")
            return False

        try:
            # Delete the point by ID
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=[image_id]
            )

            logger.info(f"Deleted image {image_id} from Qdrant")
            return True

        except Exception as e:
            logger.error(f"Error deleting image {image_id} from Qdrant: {e}")
            return False

    def get_image_by_id(self, image_id: str) -> Optional[Dict]:
        """Get a specific image by its ID"""
        if not self.is_enabled():
            return None

        try:
            # Retrieve the specific point
            points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=[image_id],
                with_payload=True,
                with_vectors=False
            )

            if points and len(points) > 0:
                point = points[0]
                nested_metadata = point.payload.get('metadata', {})

                # Construct image URL if image_file exists
                if 'image_file' in nested_metadata and nested_metadata['image_file']:
                    cdn_base_url = current_app.config.get('CDN_BASE_URL', 'https://cdn.mirailabs.co/partnr/ai-generated')
                    enhanced_metadata = nested_metadata.copy()
                    enhanced_metadata['image_url'] = f"{cdn_base_url}/{nested_metadata['image_file']}"
                else:
                    enhanced_metadata = nested_metadata

                return {
                    'id': str(point.id),
                    'text': point.payload.get('text', ''),
                    'metadata': enhanced_metadata
                }

            return None

        except Exception as e:
            logger.error(f"Error retrieving image {image_id}: {e}")
            return None

# Global service instance
qdrant_service = QdrantService()
