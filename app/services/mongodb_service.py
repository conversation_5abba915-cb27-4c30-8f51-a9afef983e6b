#!/usr/bin/env python3
"""
MongoDB Service for Character CMS
Handles MongoDB connection, synchronization, and data operations
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from flask import current_app
from bson import ObjectId

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, PyMongoError
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    MongoClient = None
    ConnectionFailure = Exception
    ServerSelectionTimeoutError = Exception
    PyMongoError = Exception

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MongoDBService:
    """MongoDB service for character data synchronization"""
    
    def __init__(self, app=None):
        self.client = None
        self.db = None
        self.collection = None
        self.enabled = False
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize MongoDB service with Flask app"""
        if not PYMONGO_AVAILABLE:
            logger.warning("PyMongo not available. MongoDB features disabled.")
            app.config['MONGODB_ENABLED'] = False
            return
        
        self.mongodb_uri = app.config.get('MONGODB_URI')
        self.database_name = app.config.get('MONGODB_DATABASE', 'character_cms')
        self.collection_name = app.config.get('MONGODB_COLLECTION', 'characters')
        self.enabled = app.config.get('MONGODB_ENABLED', False)
        
        if self.enabled and self.mongodb_uri:
            try:
                self._connect()
                logger.info("MongoDB service initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize MongoDB: {e}")
                self.enabled = False
                app.config['MONGODB_ENABLED'] = False
        else:
            logger.info("MongoDB not configured. Running in SQLite-only mode.")
    
    def _connect(self):
        """Establish MongoDB connection"""
        if not self.mongodb_uri:
            raise ValueError("MongoDB URI not configured")
        
        try:
            # Create MongoDB client with timeout settings
            self.client = MongoClient(
                self.mongodb_uri,
                serverSelectionTimeoutMS=5000,  # 5 second timeout
                connectTimeoutMS=5000,
                socketTimeoutMS=5000
            )
            
            # Test connection
            self.client.admin.command('ping')
            
            # Get database and collection
            self.db = self.client[self.database_name]
            self.collection = self.db[self.collection_name]
            
            logger.info(f"Connected to MongoDB: {self.database_name}.{self.collection_name}")
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"MongoDB connection failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected MongoDB error: {e}")
            raise
    
    def is_enabled(self) -> bool:
        """Check if MongoDB service is enabled and connected"""
        return self.enabled and self.client is not None
    
    def test_connection(self) -> bool:
        """Test MongoDB connection"""
        if not self.is_enabled():
            return False
        
        try:
            self.client.admin.command('ping')
            return True
        except Exception as e:
            logger.error(f"MongoDB connection test failed: {e}")
            return False
    
    def get_all_characters(self) -> List[Dict]:
        """Retrieve all characters from MongoDB"""
        if not self.is_enabled():
            logger.warning("MongoDB not enabled. Cannot retrieve characters.")
            return []
        
        try:
            characters = list(self.collection.find({}))
            logger.info(f"Retrieved {len(characters)} characters from MongoDB")
            return characters
        except PyMongoError as e:
            logger.error(f"Error retrieving characters from MongoDB: {e}")
            return []
    
    def get_character_by_id(self, character_id: str) -> Optional[Dict]:
        """Retrieve a specific character by character_id"""
        if not self.is_enabled():
            return None
        
        try:
            character = self.collection.find_one({"character_id": character_id})
            if character:
                logger.info(f"Retrieved character '{character_id}' from MongoDB")
            return character
        except PyMongoError as e:
            logger.error(f"Error retrieving character '{character_id}' from MongoDB: {e}")
            return None
    
    def insert_character(self, character_data: Dict) -> Optional[str]:
        """Insert a new character into MongoDB"""
        if not self.is_enabled():
            logger.warning("MongoDB not enabled. Cannot insert character.")
            return None
        
        try:
            # Remove SQLite-specific fields
            mongo_data = self._prepare_character_for_mongo(character_data)
            
            result = self.collection.insert_one(mongo_data)
            logger.info(f"Inserted character '{character_data.get('character_id')}' into MongoDB")
            return str(result.inserted_id)
        except PyMongoError as e:
            logger.error(f"Error inserting character into MongoDB: {e}")
            return None
    
    def update_character(self, character_id: str, character_data: Dict) -> bool:
        """Update an existing character in MongoDB"""
        if not self.is_enabled():
            logger.warning("MongoDB not enabled. Cannot update character.")
            return False

        try:
            # Debug: Log original data
            logger.debug(f"Original character_data for {character_id}: {character_data}")

            # Remove SQLite-specific fields
            mongo_data = self._prepare_character_for_mongo(character_data)

            # Debug: Log prepared data
            logger.debug(f"Prepared mongo_data for {character_id}: {mongo_data}")

            if not mongo_data:
                logger.warning(f"No valid data to update for character '{character_id}' after preparation")
                return False

            result = self.collection.update_one(
                {"character_id": character_id},
                {"$set": mongo_data}
            )

            # Debug: Log update result
            logger.debug(f"MongoDB update result for {character_id}: matched={result.matched_count}, modified={result.modified_count}")

            if result.matched_count > 0:
                if result.modified_count > 0:
                    logger.info(f"Updated character '{character_id}' in MongoDB ({result.modified_count} fields changed)")
                else:
                    logger.info(f"Character '{character_id}' in MongoDB is already up to date")
                return True
            else:
                logger.warning(f"No character '{character_id}' found to update in MongoDB")
                return False
        except PyMongoError as e:
            logger.error(f"Error updating character '{character_id}' in MongoDB: {e}")
            return False
    
    def delete_character(self, character_id: str) -> bool:
        """Delete a character from MongoDB"""
        if not self.is_enabled():
            logger.warning("MongoDB not enabled. Cannot delete character.")
            return False
        
        try:
            result = self.collection.delete_one({"character_id": character_id})
            
            if result.deleted_count > 0:
                logger.info(f"Deleted character '{character_id}' from MongoDB")
                return True
            else:
                logger.warning(f"No character '{character_id}' found to delete in MongoDB")
                return False
        except PyMongoError as e:
            logger.error(f"Error deleting character '{character_id}' from MongoDB: {e}")
            return False
    
    def _prepare_character_for_mongo(self, character_data: Dict) -> Dict:
        """Prepare character data for MongoDB storage"""
        # Remove SQLite-specific fields
        exclude_fields = {
            'id', 'created_by', 'created_at', 'updated_at', 
            'description', 'attributes', 'template_image', 'thumbnail'
        }
        
        mongo_data = {}
        for key, value in character_data.items():
            if key not in exclude_fields and value is not None:
                # Handle match_rate_roleset conversion
                if key == 'match_rate_roleset' and isinstance(value, str):
                    try:
                        mongo_data[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        mongo_data[key] = []
                else:
                    mongo_data[key] = value
        
        return mongo_data
    
    def get_mongodb_field_mapping(self) -> Dict[str, str]:
        """Get mapping between MongoDB fields and SQLite fields"""
        return {
            # MongoDB field -> SQLite field
            'character_id': 'character_id',
            'name': 'name',
            'age': 'age',
            'occupation': 'occupation',
            'country': 'country',
            'hobbies': 'hobbies',
            'hair_color': 'hair_color',
            'hair_style': 'hair_style',
            'eye_color': 'eye_color',
            'eye_type': 'eye_type',
            'face_detail': 'face_detail',
            'body_detail': 'body_detail',
            'skin_color': 'skin_color',
            'personality_roleplay': 'personality_roleplay',
            'style_roleplay': 'style_roleplay',
            'nsfw_style': 'nsfw_style',
            'timezone': 'timezone',
            'gender': 'gender',
            'types': 'types',
            'prompt_gen_image': 'prompt_gen_image',
            'prompt_negative_gen_image': 'prompt_negative_gen_image',
            'match_rate_roleset': 'match_rate_roleset',
            'bio': 'bio',
            'scenario_information': 'scenario_information',
            'prompt_gen_scenario_image': 'prompt_gen_scenario_image',
            'voice_id': 'voice_id'
        }
    
    def get_mongodb_fields(self) -> List[str]:
        """Get list of MongoDB fields that should be displayed in UI"""
        return list(self.get_mongodb_field_mapping().keys())
    
    def close_connection(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")

# Global MongoDB service instance
mongodb_service = MongoDBService()
