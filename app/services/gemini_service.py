from llama_index.embeddings.vertex import VertexTextEmbedding
from google.oauth2 import service_account
from llama_index.core import VectorStoreIndex
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from llama_index.core.schema import TextNode
import requests
import json
from typing import List, Dict
import datetime
from loguru import logger
import os
import base64
from dotenv import load_dotenv
import uuid

load_dotenv()


class LLMVertexEmbedding:
    def __init__(self, json_config: str, model_name: str = "text-embedding-large-exp-03-07",
                 location: str = "us-central1"):
        self.json_config = json_config
        self.model_name = model_name
        self.location = location
        self.credentials = None
        self.embedding = None
        self.init()

    def init(self):
        self.credentials = service_account.Credentials.from_service_account_file(self.json_config)
        self.embedding = VertexTextEmbedding(
            model_name=self.model_name,
            project=self.credentials.project_id,
            location=self.location,
            credentials=self.credentials,
        )

    def get_embedding_model(self):
        return self.embedding

    async def get_embedding_text(self, content: str) -> List:
        return await self.embedding.aget_text_embedding(content)


class LlamaIndexQdrant:
    def __init__(self, url_qdrant: str, collection_qdrant: str, top_k=20):
        self.client = QdrantClient(url_qdrant)
        self.collection_name = collection_qdrant
        self.vector_store = QdrantVectorStore(client=self.client, collection_name=self.collection_name)
        self.top_k = top_k
        self.embedding_model = None
        self.index = None
        self.init()

    def init(self):
        self.embedding_model = LLMVertexEmbedding(os.environ["JSON_CONFIG_CREDENTIALS"])
        self.index = VectorStoreIndex.from_vector_store(vector_store=self.vector_store,
                                                        embed_model=self.embedding_model.get_embedding_model())

    async def test_embedding(self):
        matrix = await self.embedding_model.get_embedding_text("hallo")
        print(matrix)

    @staticmethod
    def convert_img_base64(image_path):
        if not os.path.exists(image_path):
            print(f"Error: The file '{image_path}' was not found.")
            return None
        try:
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
                encoded_bytes = base64.b64encode(image_bytes)
                encoded_string = encoded_bytes.decode('utf-8')
                return encoded_string

        except Exception as e:
            print(f"An error occurred during conversion: {e}")
            return None

    async def insert(self, content: str, metadata: Dict):
        try:
            logger.info("Start inserting one content to qdrant")
            nodes = []
            node_id = str(uuid.uuid4())
            node = TextNode(
                text=content,
                id_=node_id,
                metadata=metadata
            )
            nodes.append(node)
            embeddings = await self.embedding_model.get_embedding_text(content)
            points = []
            for i, node in enumerate(nodes):
                points.append({
                    "id": node.id_,
                    "vector": embeddings[i],
                    "payload": {
                        "text": node.text,
                        "metadata": node.metadata,
                        "timestamp": datetime.datetime.now().strftime("%d-%m-%Y  %H:%M:%S")
                    }
                })
            logger.info("Create node successfully")
            # Bulk insert using Qdrant client directly
            self.client.upsert(
                collection_name=self.collection_name,
                points=points,
                wait=True
            )
            logger.info("Insert one point to qdrant successfully")
            return True
        except Exception as e:
            logger.exception(e)
            return None

    async def upload_image(self, path_image):
        try:
            url = os.environ.get("API_UPLOAD_IMAGE", "")
            content_image = LlamaIndexQdrant.convert_img_base64(path_image)
            filename = f"{str(uuid.uuid4())}.png"
            payload = json.dumps({
                "api_key": "partnr-ai-api-secret-1234",
                "base64": content_image,
                "file_name": filename
            })
            headers = {
                'accept': '*/*',
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code == 200:
                return filename
            else:
                return None
        except Exception as e:
            logger.exception(e)
            return None

    async def process_upload(self, character_id: str, description: str, path_file: str, prompt_image: Dict, scenario_mode: bool):
        try:
            file_name = await self.upload_image(path_file)
            if file_name is None:
                return None
            # create metadata
            metadata = {
                "image_file": file_name,
                "user_id": "upload-cms",
                "character_id": character_id,
                "session_id": "upload-custom-cms",
                "type": "upload",
                "image_link": "",
                "type_nsfw": False,
                "prompt_image": prompt_image,
                "scenario_mode": scenario_mode
            }
            result = await self.insert(description, metadata)
            if result:
                return True
            else:
                return None
        except Exception as e:
            return None


if __name__ == '__main__':
    import asyncio

    qdrant = LlamaIndexQdrant("http://**************:6333/", "partnr-chat-dev")
    asyncio.run(qdrant.test_embedding())
