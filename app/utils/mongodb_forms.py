#!/usr/bin/env python3
"""
MongoDB Dynamic Form Generator
Creates forms based on MongoDB field availability
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, TextAreaField, IntegerField, SelectField, SubmitField, SelectMultipleField
from wtforms.validators import DataRequired, Length, Optional, NumberRange



from app.utils.character_types import get_all_character_types

class MongoDBCharacterForm(FlaskForm):
    """Dynamic character form based on MongoDB fields"""

    # Define all possible fields as class attributes to avoid UnboundField issues
    # Basic Information
    character_id = StringField('Character ID', validators=[Optional(), Length(max=100)])
    name = StringField('Name', validators=[DataRequired(), Length(min=1, max=100)])
    age = IntegerField('Age', validators=[Optional(), NumberRange(min=1, max=999)])
    occupation = StringField('Occupation', validators=[Optional(), Length(max=200)])
    country = StringField('Country/Location', validators=[Optional(), Length(max=200)])
    hobbies = StringField('Hobbies', validators=[Optional(), Length(max=500)])
    bio = TextAreaField('Bio', validators=[Optional()], render_kw={'rows': 4})

    # Physical Appearance
    hair_color = StringField('Hair Color', validators=[Optional(), Length(max=100)])
    hair_style = StringField('Hair Style', validators=[Optional(), Length(max=100)])
    eye_color = StringField('Eye Color', validators=[Optional(), Length(max=100)])
    eye_type = StringField('Eye Type', validators=[Optional(), Length(max=200)])
    face_detail = TextAreaField('Face Details', validators=[Optional()], render_kw={'rows': 3})
    body_detail = TextAreaField('Body Details', validators=[Optional()], render_kw={'rows': 3})
    skin_color = StringField('Skin Color', validators=[Optional(), Length(max=100)])

    # Personality & Roleplay
    personality_roleplay = TextAreaField('Personality & Roleplay', validators=[Optional()], render_kw={'rows': 4})
    style_roleplay = TextAreaField('Communication Style', validators=[Optional()], render_kw={'rows': 3})
    nsfw_style = TextAreaField('NSFW Style', validators=[Optional()], render_kw={'rows': 3})
    scenario_information = TextAreaField('Scenario Information', validators=[Optional()], render_kw={'rows': 4})
    match_rate_roleset = StringField('Match Rate Roleset', validators=[Optional()])

    # AI Generation
    prompt_gen_image = TextAreaField('Image Generation Prompt', validators=[Optional()], render_kw={'rows': 4})
    prompt_negative_gen_image = TextAreaField('Negative Image Prompt', validators=[Optional()], render_kw={'rows': 3})
    prompt_gen_scenario_image = TextAreaField('Scenario Image Prompt', validators=[Optional()], render_kw={'rows': 3})

    # System Information
    timezone = IntegerField('Timezone', validators=[Optional(), NumberRange(min=-12, max=14)])
    gender = SelectField('Gender', choices=[
        ('', 'Select Gender'),
        ('Male', 'Male'),
        ('Female', 'Female'),
        ('Non-binary', 'Non-binary'),
        ('Other', 'Other')
    ], validators=[Optional()])
    types = SelectField('Character Type', choices=[], validators=[Optional()])
    voice_id = StringField('Voice ID', validators=[Optional(), Length(max=100)])

    # Required fields for compatibility
    tags = SelectMultipleField('Tags', coerce=int)
    template_image = FileField('Template Image', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif', 'webp'], 'Images only!')
    ])
    description = TextAreaField('Description (Legacy)', validators=[Optional()])
    attributes = TextAreaField('Attributes (JSON format)', validators=[Optional()])

    # Submit button
    submit = SubmitField('Save Character')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._populate_dynamic_choices()
    

    


    def _populate_dynamic_choices(self):
        """Populate dynamic choices for select fields"""
        # Populate character types
        if hasattr(self, 'types'):
            character_types = get_all_character_types()
            choices = [('', 'Select Type')] + [(t, t) for t in character_types]
            self.types.choices = choices

        # Populate tags choices if field exists
        if hasattr(self, 'tags'):
            from app.models import Tag
            self.tags.choices = [(tag.id, tag.name) for tag in Tag.query.all()]

    def get_field_categories(self):
        """Get fields organized by categories for tabbed display"""
        return self._get_all_categories()
    
    def _get_all_categories(self):
        """Get all field categories"""
        return {
            'Basic Information': [
                'character_id', 'name', 'age', 'occupation', 'country', 'hobbies', 'bio'
            ],
            'Physical Appearance': [
                'hair_color', 'hair_style', 'eye_color', 'eye_type', 
                'face_detail', 'body_detail', 'skin_color'
            ],
            'Personality & Roleplay': [
                'personality_roleplay', 'style_roleplay', 'nsfw_style', 
                'scenario_information', 'match_rate_roleset'
            ],
            'AI Generation': [
                'prompt_gen_image', 'prompt_negative_gen_image', 'prompt_gen_scenario_image'
            ],
            'System Information': [
                'timezone', 'gender', 'types', 'voice_id'
            ]
        }
