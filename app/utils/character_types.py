#!/usr/bin/env python3
"""
Character Types Configuration
Configurable character type system for the Character CMS
"""

# Character Types Configuration
CHARACTER_TYPES = {
    # Visual Styles
    'visual_styles': {
        'label': 'Visual Styles',
        'types': [
            ('Anime', 'Anime/Manga Style'),
            ('Realistic', 'Realistic/Photorealistic'),
            ('Cartoon', 'Cartoon/Animated'),
            ('Semi-Realistic', 'Semi-Realistic'),
            ('Chibi', 'Chibi/Super Deformed'),
            ('Pixel Art', 'Pixel Art Style'),
            ('Oil Painting', 'Oil Painting Style'),
            ('Watercolor', 'Watercolor Style'),
            ('Sketch', 'Sketch/Line Art'),
            ('3D Render', '3D Rendered')
        ]
    },
    
    # Genre/Theme
    'genres': {
        'label': 'Genres & Themes',
        'types': [
            ('Fantasy', 'Fantasy/Magical'),
            ('Sci-Fi', 'Science Fiction'),
            ('Modern', 'Modern/Contemporary'),
            ('Historical', 'Historical Period'),
            ('Cyberpunk', 'Cyberpunk/Futuristic'),
            ('Steampunk', 'Steampunk/Victorian'),
            ('Post-Apocalyptic', 'Post-Apocalyptic'),
            ('Medieval', 'Medieval/Middle Ages'),
            ('Victorian', 'Victorian Era'),
            ('Wild West', 'Wild West/Western'),
            ('Space Opera', 'Space Opera'),
            ('Urban Fantasy', 'Urban Fantasy'),
            ('Horror', 'Horror/Dark'),
            ('Romance', 'Romance/Love'),
            ('Adventure', 'Adventure/Action'),
            ('Mystery', 'Mystery/Detective'),
            ('Slice of Life', 'Slice of Life')
        ]
    },
    
    # Character Archetypes
    'archetypes': {
        'label': 'Character Archetypes',
        'types': [
            ('Hero', 'Hero/Protagonist'),
            ('Villain', 'Villain/Antagonist'),
            ('Anti-Hero', 'Anti-Hero'),
            ('Mentor', 'Mentor/Teacher'),
            ('Sidekick', 'Sidekick/Companion'),
            ('Love Interest', 'Love Interest'),
            ('Rival', 'Rival/Competitor'),
            ('Trickster', 'Trickster/Joker'),
            ('Guardian', 'Guardian/Protector'),
            ('Rebel', 'Rebel/Outlaw'),
            ('Scholar', 'Scholar/Intellectual'),
            ('Warrior', 'Warrior/Fighter'),
            ('Healer', 'Healer/Medic'),
            ('Leader', 'Leader/Commander'),
            ('Innocent', 'Innocent/Pure'),
            ('Sage', 'Sage/Wise One'),
            ('Explorer', 'Explorer/Adventurer'),
            ('Creator', 'Creator/Artist'),
            ('Ruler', 'Ruler/Royalty'),
            ('Caregiver', 'Caregiver/Nurturer')
        ]
    },
    
    # Personality Types
    'personalities': {
        'label': 'Personality Types',
        'types': [
            ('Extrovert', 'Extroverted/Outgoing'),
            ('Introvert', 'Introverted/Reserved'),
            ('Optimist', 'Optimistic/Positive'),
            ('Pessimist', 'Pessimistic/Negative'),
            ('Confident', 'Confident/Self-Assured'),
            ('Shy', 'Shy/Timid'),
            ('Aggressive', 'Aggressive/Assertive'),
            ('Passive', 'Passive/Gentle'),
            ('Serious', 'Serious/Stoic'),
            ('Playful', 'Playful/Fun-loving'),
            ('Mysterious', 'Mysterious/Enigmatic'),
            ('Cheerful', 'Cheerful/Happy'),
            ('Melancholic', 'Melancholic/Sad'),
            ('Energetic', 'Energetic/Active'),
            ('Calm', 'Calm/Peaceful'),
            ('Impulsive', 'Impulsive/Spontaneous'),
            ('Calculating', 'Calculating/Strategic'),
            ('Loyal', 'Loyal/Faithful'),
            ('Independent', 'Independent/Self-reliant'),
            ('Caring', 'Caring/Compassionate')
        ]
    },
    
    # Age Categories
    'age_categories': {
        'label': 'Age Categories',
        'types': [
            ('Child', 'Child (0-12)'),
            ('Teen', 'Teenager (13-19)'),
            ('Young Adult', 'Young Adult (20-29)'),
            ('Adult', 'Adult (30-49)'),
            ('Middle-aged', 'Middle-aged (50-64)'),
            ('Senior', 'Senior (65+)'),
            ('Immortal', 'Immortal/Ageless'),
            ('Ancient', 'Ancient Being'),
            ('Unknown Age', 'Unknown/Variable Age')
        ]
    },
    
    # Species/Race
    'species': {
        'label': 'Species & Races',
        'types': [
            ('Human', 'Human'),
            ('Elf', 'Elf/Elven'),
            ('Dwarf', 'Dwarf/Dwarven'),
            ('Orc', 'Orc/Orcish'),
            ('Halfling', 'Halfling/Hobbit'),
            ('Dragon', 'Dragon/Draconic'),
            ('Angel', 'Angel/Celestial'),
            ('Demon', 'Demon/Infernal'),
            ('Vampire', 'Vampire'),
            ('Werewolf', 'Werewolf/Lycanthrope'),
            ('Fairy', 'Fairy/Fae'),
            ('Robot', 'Robot/Android'),
            ('Alien', 'Alien/Extraterrestrial'),
            ('Ghost', 'Ghost/Spirit'),
            ('Zombie', 'Zombie/Undead'),
            ('Cyborg', 'Cyborg/Enhanced Human'),
            ('Mutant', 'Mutant/Enhanced'),
            ('Shapeshifter', 'Shapeshifter'),
            ('Elemental', 'Elemental Being'),
            ('Beast', 'Beast/Animal'),
            ('Hybrid', 'Hybrid/Mixed Species'),
            ('Golem', 'Golem/Construct'),
            ('Neko', 'Neko/Cat-person'),
            ('Kitsune', 'Kitsune/Fox-spirit'),
            ('Other', 'Other/Custom Species')
        ]
    }
}

def get_all_character_types():
    """Get all character types as a flat list for form choices"""
    choices = [('', 'Select Character Type')]
    
    for category_key, category_data in CHARACTER_TYPES.items():
        # Add category separator
        choices.append((f'--- {category_data["label"]} ---', f'--- {category_data["label"]} ---'))
        
        # Add types in this category
        for type_key, type_label in category_data['types']:
            choices.append((type_key, type_label))
    
    return choices

def get_character_types_by_category():
    """Get character types organized by category"""
    return CHARACTER_TYPES

def get_character_type_categories():
    """Get list of character type categories"""
    return [(key, data['label']) for key, data in CHARACTER_TYPES.items()]

def validate_character_type(character_type):
    """Validate if a character type exists in the configuration"""
    if not character_type:
        return True  # Empty is valid
    
    for category_data in CHARACTER_TYPES.values():
        for type_key, _ in category_data['types']:
            if type_key == character_type:
                return True
    
    return False

def get_character_type_label(character_type):
    """Get the full label for a character type"""
    if not character_type:
        return None
    
    for category_data in CHARACTER_TYPES.values():
        for type_key, type_label in category_data['types']:
            if type_key == character_type:
                return type_label
    
    return character_type  # Return original if not found

def get_character_types_for_category(category_key):
    """Get character types for a specific category"""
    if category_key in CHARACTER_TYPES:
        return CHARACTER_TYPES[category_key]['types']
    return []

def search_character_types(query):
    """Search character types by query string"""
    query = query.lower()
    results = []
    
    for category_key, category_data in CHARACTER_TYPES.items():
        for type_key, type_label in category_data['types']:
            if query in type_key.lower() or query in type_label.lower():
                results.append({
                    'key': type_key,
                    'label': type_label,
                    'category': category_data['label']
                })
    
    return results

# Popular/Default character types for quick selection
POPULAR_CHARACTER_TYPES = [
    'Anime',
    'Realistic', 
    'Fantasy',
    'Modern',
    'Sci-Fi',
    'Hero',
    'Love Interest',
    'Human',
    'Young Adult',
    'Confident',
    'Cheerful'
]

def get_popular_character_types():
    """Get popular character types for quick selection"""
    choices = []
    for type_key in POPULAR_CHARACTER_TYPES:
        label = get_character_type_label(type_key)
        if label:
            choices.append((type_key, label))
    return choices
