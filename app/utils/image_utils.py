import os
import uuid
from PIL import Image, ImageOps
from werkzeug.utils import secure_filename
from flask import current_app

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def generate_unique_filename(filename):
    """Generate a unique filename while preserving the extension"""
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    unique_name = str(uuid.uuid4())
    return f"{unique_name}.{ext}" if ext else unique_name

def create_thumbnail(image_path, thumbnail_path, size=None):
    """Create a thumbnail from an image"""
    if size is None:
        size = current_app.config['THUMBNAIL_SIZE']
    
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary (for PNG with transparency)
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Create thumbnail maintaining aspect ratio
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Create a square thumbnail with padding if needed
            thumb = Image.new('RGB', size, (255, 255, 255))
            thumb_w, thumb_h = img.size
            offset = ((size[0] - thumb_w) // 2, (size[1] - thumb_h) // 2)
            thumb.paste(img, offset)
            
            # Save thumbnail
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            thumb.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
            
        return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False

def get_image_info(image_path):
    """Get image dimensions and file size"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
        file_size = os.path.getsize(image_path)
        return width, height, file_size
    except Exception as e:
        print(f"Error getting image info: {e}")
        return None, None, None

def optimize_image(image_path, max_size=(1920, 1920), quality=85):
    """Optimize image size and quality"""
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Resize if larger than max_size
            if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Save optimized image
            img.save(image_path, 'JPEG', quality=quality, optimize=True)
        
        return True
    except Exception as e:
        print(f"Error optimizing image: {e}")
        return False
