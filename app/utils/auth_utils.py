"""
Authentication and Authorization Utilities for Character CMS
"""

from functools import wraps
from flask import flash, redirect, url_for, request, abort, jsonify
from flask_login import current_user
import logging

logger = logging.getLogger(__name__)

def login_required_with_message(message="Please log in to access this page.", category="info"):
    """
    Enhanced login_required decorator with custom messages
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash(message, category)
                logger.info(f"Unauthenticated access attempt to {request.endpoint} by {request.remote_addr}")
                return redirect(url_for('auth.login', next=request.url))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """
    Decorator to require admin access with proper error handling
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'info')
            logger.warning(f"Unauthenticated admin access attempt to {request.endpoint}")
            return redirect(url_for('auth.login', next=request.url))
        
        if not current_user.is_admin:
            flash('Administrator privileges required to access this page.', 'danger')
            logger.warning(f"Non-admin user {current_user.username} attempted to access {request.endpoint}")
            return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function

def owner_or_admin_required(get_owner_func):
    """
    Decorator to require ownership or admin access
    get_owner_func should return the user_id of the owner
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('Please log in to access this page.', 'info')
                return redirect(url_for('auth.login', next=request.url))
            
            # Get the owner ID using the provided function
            owner_id = get_owner_func(*args, **kwargs)
            
            if current_user.id != owner_id and not current_user.is_admin:
                flash('You do not have permission to access this resource.', 'danger')
                logger.warning(f"User {current_user.username} attempted unauthorized access to {request.endpoint}")
                return redirect(url_for('main.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def api_login_required(f):
    """
    API-specific login required decorator that returns JSON responses
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            logger.info(f"Unauthenticated API access attempt to {request.endpoint}")
            return jsonify({'error': 'Authentication required', 'login_url': url_for('auth.login')}), 401
        return f(*args, **kwargs)
    return decorated_function

def api_admin_required(f):
    """
    API-specific admin required decorator that returns JSON responses
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return jsonify({'error': 'Authentication required', 'login_url': url_for('auth.login')}), 401
        
        if not current_user.is_admin:
            logger.warning(f"Non-admin API access attempt by {current_user.username} to {request.endpoint}")
            return jsonify({'error': 'Administrator privileges required'}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def api_owner_or_admin_required(get_owner_func):
    """
    API-specific owner or admin required decorator
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return jsonify({'error': 'Authentication required', 'login_url': url_for('auth.login')}), 401
            
            owner_id = get_owner_func(*args, **kwargs)
            
            if current_user.id != owner_id and not current_user.is_admin:
                logger.warning(f"Unauthorized API access by {current_user.username} to {request.endpoint}")
                return jsonify({'error': 'Permission denied'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_character_ownership(character_id):
    """
    Helper function to check character ownership
    """
    from app.models import Character
    character = Character.query.get_or_404(character_id)
    return character.created_by

def check_image_ownership(image_id):
    """
    Helper function to check image ownership
    """
    from app.models import GeneratedImage
    image = GeneratedImage.query.get_or_404(image_id)
    return image.uploaded_by

def require_ownership_or_admin(resource_type, resource_id):
    """
    Generic function to check ownership or admin status
    """
    if not current_user.is_authenticated:
        return False, "Authentication required"
    
    if current_user.is_admin:
        return True, "Admin access granted"
    
    if resource_type == 'character':
        owner_id = check_character_ownership(resource_id)
    elif resource_type == 'image':
        owner_id = check_image_ownership(resource_id)
    else:
        return False, "Unknown resource type"
    
    if current_user.id == owner_id:
        return True, "Owner access granted"
    
    return False, "Permission denied"

def log_access_attempt(endpoint, success=True, reason=""):
    """
    Log access attempts for security monitoring
    """
    user_info = f"User: {current_user.username}" if current_user.is_authenticated else "Anonymous"
    status = "SUCCESS" if success else "DENIED"
    
    log_message = f"Access {status} - Endpoint: {endpoint} - {user_info}"
    if reason:
        log_message += f" - Reason: {reason}"
    
    if success:
        logger.info(log_message)
    else:
        logger.warning(log_message)

def get_user_permissions():
    """
    Get current user's permissions for template rendering
    """
    if not current_user.is_authenticated:
        return {
            'can_create_character': False,
            'can_upload_image': False,
            'can_access_admin': False,
            'can_bulk_upload': False,
            'is_authenticated': False,
            'is_admin': False
        }
    
    return {
        'can_create_character': True,
        'can_upload_image': True,
        'can_access_admin': current_user.is_admin,
        'can_bulk_upload': True,
        'is_authenticated': True,
        'is_admin': current_user.is_admin
    }

def can_edit_character(character):
    """
    Check if current user can edit a specific character
    Allows all authenticated users to edit any character
    """
    if not current_user.is_authenticated:
        return False
    # Allow all authenticated users to edit any character
    return True

def can_delete_character(character):
    """
    Check if current user can delete a specific character
    """
    if not current_user.is_authenticated:
        return False
    return current_user.is_admin or character.created_by == current_user.id

def can_edit_image(image):
    """
    Check if current user can edit a specific image
    """
    if not current_user.is_authenticated:
        return False
    return current_user.is_admin or image.uploaded_by == current_user.id

def can_delete_image(image):
    """
    Check if current user can delete a specific image
    """
    if not current_user.is_authenticated:
        return False
    return current_user.is_admin or image.uploaded_by == current_user.id
