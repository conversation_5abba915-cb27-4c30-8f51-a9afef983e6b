import os
import json
from flask import Blueprint, render_template, request, flash, redirect, url_for, current_app, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, SubmitField, SelectMultipleField
from wtforms.validators import DataRequired, Length
from werkzeug.utils import secure_filename
from app.models import Character, Tag, GeneratedImage
from app.utils.image_utils import allowed_file, generate_unique_filename, create_thumbnail
from app.utils.auth_utils import (
    login_required_with_message,
    owner_or_admin_required,
    check_character_ownership,
    log_access_attempt
)
from app.utils.mongodb_forms import MongoDBCharacterForm
from app.services import mongodb_service, character_sync_service, qdrant_service
from app.utils.character_types import get_all_character_types
from app import db

characters = Blueprint('characters', __name__)

class CharacterForm(FlaskForm):
    # Basic Information
    character_id = StringField('Character ID', validators=[Length(max=100)])
    name = StringField('Name', validators=[DataRequired(), Length(min=1, max=100)])
    age = StringField('Age')
    occupation = StringField('Occupation', validators=[Length(max=200)])
    country = StringField('Country/Location', validators=[Length(max=200)])
    hobbies = StringField('Hobbies', validators=[Length(max=500)])

    # Physical Appearance
    hair_color = StringField('Hair Color', validators=[Length(max=100)])
    hair_style = StringField('Hair Style', validators=[Length(max=100)])
    eye_color = StringField('Eye Color', validators=[Length(max=100)])
    eye_type = StringField('Eye Type', validators=[Length(max=200)])
    face_detail = TextAreaField('Face Details')
    body_detail = TextAreaField('Body Details')
    skin_color = StringField('Skin Color', validators=[Length(max=100)])

    # Personality & Roleplay
    personality_roleplay = TextAreaField('Personality & Roleplay')
    style_roleplay = TextAreaField('Communication Style')
    nsfw_style = TextAreaField('NSFW Style (Optional)')

    # System Information
    timezone = StringField('Timezone (UTC offset)')
    gender = StringField('Gender', validators=[Length(max=50)])
    types = StringField('Character Type (e.g., Anime, Realistic)', validators=[Length(max=100)])

    # AI Generation Prompts
    prompt_gen_image = TextAreaField('Image Generation Prompt')
    prompt_negative_gen_image = TextAreaField('Negative Prompt for Images')
    prompt_gen_scenario_image = TextAreaField('Scenario Image Prompt')

    # Additional Information
    bio = TextAreaField('Bio/Profile')
    scenario_information = TextAreaField('Scenario Information')
    voice_id = StringField('Voice ID', validators=[Length(max=100)])
    match_rate_roleset = TextAreaField('Match Rate Roleset (comma-separated)')

    # Legacy fields
    description = TextAreaField('Description (Legacy)')
    attributes = TextAreaField('Attributes (JSON format)')
    template_image = FileField('Template Image', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif', 'webp'], 'Images only!')
    ])
    tags = SelectMultipleField('Tags', coerce=int)
    submit = SubmitField('Save Character')

@characters.route('/')
@login_required_with_message("Please log in to browse characters.", "info")
def index():
    """List all characters with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    tag_filter = request.args.get('tag', type=int)
    search = request.args.get('search', '').strip()

    query = Character.query

    # Apply filters
    if tag_filter:
        query = query.filter(Character.tags.any(Tag.id == tag_filter))

    if search:
        query = query.filter(Character.name.contains(search) |
                           Character.description.contains(search))

    characters_pagination = query.order_by(Character.created_at.desc()).paginate(
        page=page, per_page=current_app.config['CHARACTERS_PER_PAGE'],
        error_out=False
    )

    # Get Qdrant data for each character if enabled
    characters_with_qdrant_data = []
    for character in characters_pagination.items:
        character_data = {
            'character': character,
            'qdrant_image_count': 0,
            'qdrant_avatar': None
        }

        if qdrant_service.is_enabled():
            try:
                # Enhanced character ID mapping logic - consistent with view route
                character_id = None

                # First, try the character_id field if it exists and has a value
                if hasattr(character, 'character_id') and character.character_id:
                    character_id = str(character.character_id).strip()

                # Fallback to database ID as string
                if not character_id:
                    character_id = str(character.id)

                # Get image count
                image_count = qdrant_service.get_character_image_count(character_id)
                character_data['qdrant_image_count'] = image_count

                # Get first image for avatar if count > 0
                if image_count > 0:
                    images = qdrant_service.get_images_by_character_id(character_id, limit=1)
                    if images and len(images) > 0:
                        first_image = images[0]
                        metadata = first_image.get('metadata', {})
                        if 'image_url' in metadata and metadata['image_url']:
                            character_data['qdrant_avatar'] = metadata['image_url']

            except Exception as e:
                current_app.logger.error(f"Error getting Qdrant data for character {character.id}: {e}")

        characters_with_qdrant_data.append(character_data)

    # Get all tags for filter dropdown
    all_tags = Tag.query.all()

    return render_template('characters/index.html',
                         characters_with_data=characters_with_qdrant_data,
                         pagination=characters_pagination,
                         all_tags=all_tags,
                         current_tag=tag_filter,
                         search_query=search,
                         qdrant_enabled=qdrant_service.is_enabled())

@characters.route('/create', methods=['GET', 'POST'])
@login_required_with_message("Please log in to create characters.", "info")
def create():
    """Create a new character"""
    # Use MongoDB-aware form if MongoDB is enabled, otherwise use legacy form
    if mongodb_service.is_enabled():
        form = MongoDBCharacterForm()
        # Tags choices are automatically populated in the form's __init__ method
    else:
        form = CharacterForm()
        form.tags.choices = [(tag.id, tag.name) for tag in Tag.query.all()]
    
    if form.validate_on_submit():
        # Create character with form data
        character_data = {
            'created_by': current_user.id
        }

        # Get MongoDB fields if enabled, otherwise use all fields
        if mongodb_service.is_enabled():
            mongodb_fields = mongodb_service.get_mongodb_field_mapping()
            for mongo_field, sqlite_field in mongodb_fields.items():
                if hasattr(form, sqlite_field) and getattr(form, sqlite_field).data:
                    value = getattr(form, sqlite_field).data

                    # Handle special field types
                    if sqlite_field == 'age' and isinstance(value, str) and value.isdigit():
                        character_data[sqlite_field] = int(value)
                    elif sqlite_field == 'timezone' and isinstance(value, str) and value.lstrip('-').isdigit():
                        character_data[sqlite_field] = int(value)
                    else:
                        character_data[sqlite_field] = value
        else:
            # Legacy mode - use all form fields
            for field_name in ['character_id', 'name', 'age', 'occupation', 'country', 'hobbies',
                              'hair_color', 'hair_style', 'eye_color', 'eye_type', 'face_detail',
                              'body_detail', 'skin_color', 'personality_roleplay', 'style_roleplay',
                              'nsfw_style', 'timezone', 'gender', 'types', 'prompt_gen_image',
                              'prompt_negative_gen_image', 'prompt_gen_scenario_image', 'bio',
                              'scenario_information', 'voice_id', 'description']:
                if hasattr(form, field_name) and getattr(form, field_name).data:
                    value = getattr(form, field_name).data

                    # Handle special field types
                    if field_name == 'age' and isinstance(value, str) and value.isdigit():
                        character_data[field_name] = int(value)
                    elif field_name == 'timezone' and isinstance(value, str) and value.lstrip('-').isdigit():
                        character_data[field_name] = int(value)
                    else:
                        character_data[field_name] = value

        character = Character(**character_data)

        # Handle match_rate_roleset
        if form.match_rate_roleset.data:
            roleset_list = [item.strip() for item in form.match_rate_roleset.data.split(',') if item.strip()]
            character.set_match_rate_roleset_list(roleset_list)

        # Handle attributes JSON
        if form.attributes.data:
            try:
                json.loads(form.attributes.data)  # Validate JSON
                character.attributes = form.attributes.data
            except json.JSONDecodeError:
                flash('Invalid JSON format in attributes', 'danger')
                return render_template('characters/create_enhanced.html', form=form)
        
        # Handle file upload
        if form.template_image.data:
            file = form.template_image.data
            if allowed_file(file.filename):
                filename = generate_unique_filename(secure_filename(file.filename))
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'characters', filename)
                
                # Save file
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                file.save(file_path)
                
                # Create thumbnail
                thumbnail_filename = f"thumb_{filename}"
                thumbnail_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', thumbnail_filename)
                
                if create_thumbnail(file_path, thumbnail_path):
                    character.thumbnail = thumbnail_filename
                
                character.template_image = filename
        
        db.session.add(character)
        db.session.flush()  # Get the character ID

        # Add tags
        if hasattr(form, 'tags') and form.tags.data:
            selected_tags = Tag.query.filter(Tag.id.in_(form.tags.data)).all()
            character.tags.extend(selected_tags)

        db.session.commit()

        # Sync to MongoDB if enabled
        if mongodb_service.is_enabled() and character.character_id:
            try:
                character_sync_service.sync_to_mongodb([character.id])
                flash('Character created successfully and synced to MongoDB!', 'success')
            except Exception as e:
                flash(f'Character created successfully, but MongoDB sync failed: {str(e)}', 'warning')
        else:
            flash('Character created successfully!', 'success')

        return redirect(url_for('characters.view', id=character.id))
    
    # Pass MongoDB status and field categories to template
    mongodb_enabled = mongodb_service.is_enabled()
    field_categories = form.get_field_categories() if hasattr(form, 'get_field_categories') else {}

    return render_template('characters/create_enhanced.html',
                         form=form,
                         mongodb_enabled=mongodb_enabled,
                         field_categories=field_categories)

@characters.route('/by-id/<string:character_id>')
@login_required_with_message("Please log in to view character details.", "info")
def view_enhanced(character_id):
    """View character details by character_id string"""
    # First try to find character in SQLite by character_id
    character = Character.query.filter_by(character_id=character_id).first()

    if not character:
        # If not found in SQLite, try to get from MongoDB and show basic info
        from app.services import mongodb_service
        try:
            mongo_character = mongodb_service.get_character_by_id(character_id)
            if mongo_character:
                # Character exists in MongoDB but not in SQLite
                flash(f'Character "{character_id}" found in MongoDB but not synced to local database.', 'warning')
                return render_template('characters/mongodb_only_view.html',
                                     character=mongo_character,
                                     character_id=character_id)
            else:
                flash(f'Character "{character_id}" not found.', 'danger')
                return redirect(url_for('characters.index'))
        except Exception as e:
            current_app.logger.error(f"Error loading character from MongoDB: {e}")
            flash(f'Character "{character_id}" not found.', 'danger')
            return redirect(url_for('characters.index'))

    # Get generated images for this character using character_id
    page = request.args.get('page', 1, type=int)
    images = GeneratedImage.query.filter_by(character_id=character_id).order_by(GeneratedImage.created_at.desc()).all()

    # Manual pagination for images
    per_page = 12
    total = len(images)
    start = (page - 1) * per_page
    end = start + per_page
    page_images = images[start:end]

    # Create pagination info
    has_prev = page > 1
    has_next = end < total
    pagination_info = {
        'page': page,
        'per_page': per_page,
        'total': total,
        'has_prev': has_prev,
        'has_next': has_next,
        'prev_num': page - 1 if has_prev else None,
        'next_num': page + 1 if has_next else None
    }

    # Get Qdrant images if enabled
    qdrant_images = []
    qdrant_stats = {}
    if qdrant_service.is_enabled():
        try:
            current_app.logger.info(f"Searching Qdrant for character_id: '{character_id}'")

            # Get images from Qdrant
            qdrant_images = qdrant_service.get_images_by_character_id(character_id, limit=50)

            # Get image count
            image_count = qdrant_service.get_character_image_count(character_id)
            qdrant_stats = {
                'total_images': image_count,
                'displayed_images': len(qdrant_images),
                'character_id_used': character_id
            }

            current_app.logger.info(f"Qdrant search results: {len(qdrant_images)} images, {image_count} total count")

        except Exception as e:
            current_app.logger.error(f"Error retrieving Qdrant images for character {character_id}: {e}")
            qdrant_images = []
            qdrant_stats = {'error': str(e)}

    return render_template('characters/view_enhanced.html',
                         character=character,
                         images=page_images,
                         pagination=pagination_info,
                         qdrant_enabled=qdrant_service.is_enabled(),
                         qdrant_images=qdrant_images,
                         qdrant_stats=qdrant_stats)

@characters.route('/<int:id>')
@login_required_with_message("Please log in to view character details.", "info")
def view(id):
    """View character details by integer ID (legacy)"""
    character = Character.query.get_or_404(id)
    
    # Get generated images for this character
    page = request.args.get('page', 1, type=int)
    images_pagination = character.generated_images.order_by(GeneratedImage.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    # Get Qdrant images if enabled
    qdrant_images = []
    qdrant_stats = {}
    if qdrant_service.is_enabled():
        try:
            # Enhanced character ID mapping logic
            character_id = None

            # First, try the character_id field if it exists and has a value
            if hasattr(character, 'character_id') and character.character_id:
                character_id = str(character.character_id).strip()
                current_app.logger.info(f"Using character.character_id: '{character_id}'")

            # Fallback to database ID as string
            if not character_id:
                character_id = str(character.id)
                current_app.logger.info(f"Fallback to character.id: '{character_id}'")

            current_app.logger.info(f"Searching Qdrant for character_id: '{character_id}'")

            # Get images from Qdrant
            qdrant_images = qdrant_service.get_images_by_character_id(character_id, limit=50)

            # Get image count
            image_count = qdrant_service.get_character_image_count(character_id)
            qdrant_stats = {
                'total_images': image_count,
                'displayed_images': len(qdrant_images),
                'character_id_used': character_id
            }

            current_app.logger.info(f"Qdrant search results: {len(qdrant_images)} images, {image_count} total count")

        except Exception as e:
            current_app.logger.error(f"Error retrieving Qdrant images for character {character.id}: {e}")
            qdrant_images = []
            qdrant_stats = {'error': str(e)}

    return render_template('characters/view_enhanced.html',
                         character=character,
                         images=images_pagination.items,
                         pagination=images_pagination,
                         qdrant_enabled=qdrant_service.is_enabled(),
                         qdrant_images=qdrant_images,
                         qdrant_stats=qdrant_stats)

@characters.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required_with_message("Please log in to edit characters.", "info")
def edit(id):
    """Edit character"""
    character = Character.query.get_or_404(id)
    log_access_attempt(f"characters.edit.{id}", True, f"User {current_user.username} editing character {character.name}")
    
    form = CharacterForm(obj=character)
    form.tags.choices = [(tag.id, tag.name) for tag in Tag.query.all()]
    
    if request.method == 'GET':
        # Populate form with existing data
        form.character_id.data = character.character_id
        form.age.data = str(character.age) if character.age else ''
        form.occupation.data = character.occupation
        form.country.data = character.country
        form.hobbies.data = character.hobbies

        # Physical Appearance
        form.hair_color.data = character.hair_color
        form.hair_style.data = character.hair_style
        form.eye_color.data = character.eye_color
        form.eye_type.data = character.eye_type
        form.face_detail.data = character.face_detail
        form.body_detail.data = character.body_detail
        form.skin_color.data = character.skin_color

        # Personality & Roleplay
        form.personality_roleplay.data = character.personality_roleplay
        form.style_roleplay.data = character.style_roleplay
        form.nsfw_style.data = character.nsfw_style

        # System Information
        form.timezone.data = str(character.timezone) if character.timezone else '0'
        form.gender.data = character.gender
        form.types.data = character.types

        # AI Generation Prompts
        form.prompt_gen_image.data = character.prompt_gen_image
        form.prompt_negative_gen_image.data = character.prompt_negative_gen_image
        form.prompt_gen_scenario_image.data = character.prompt_gen_scenario_image

        # Additional Information
        form.bio.data = character.bio
        form.scenario_information.data = character.scenario_information
        form.voice_id.data = character.voice_id

        # Match rate roleset
        roleset_list = character.get_match_rate_roleset_list()
        form.match_rate_roleset.data = ', '.join(roleset_list) if roleset_list else ''

        form.tags.data = [tag.id for tag in character.tags]
    
    if form.validate_on_submit():
        # Basic Information
        character.character_id = form.character_id.data
        character.name = form.name.data
        character.age = int(form.age.data) if form.age.data and form.age.data.isdigit() else None
        character.occupation = form.occupation.data
        character.country = form.country.data
        character.hobbies = form.hobbies.data

        # Physical Appearance
        character.hair_color = form.hair_color.data
        character.hair_style = form.hair_style.data
        character.eye_color = form.eye_color.data
        character.eye_type = form.eye_type.data
        character.face_detail = form.face_detail.data
        character.body_detail = form.body_detail.data
        character.skin_color = form.skin_color.data

        # Personality & Roleplay
        character.personality_roleplay = form.personality_roleplay.data
        character.style_roleplay = form.style_roleplay.data
        character.nsfw_style = form.nsfw_style.data

        # System Information
        character.timezone = int(form.timezone.data) if form.timezone.data and form.timezone.data.lstrip('-').isdigit() else 0
        character.gender = form.gender.data
        character.types = form.types.data

        # AI Generation Prompts
        character.prompt_gen_image = form.prompt_gen_image.data
        character.prompt_negative_gen_image = form.prompt_negative_gen_image.data
        character.prompt_gen_scenario_image = form.prompt_gen_scenario_image.data

        # Additional Information
        character.bio = form.bio.data
        character.scenario_information = form.scenario_information.data
        character.voice_id = form.voice_id.data

        # Legacy fields
        character.description = form.description.data

        # Handle match_rate_roleset
        if form.match_rate_roleset.data:
            roleset_list = [item.strip() for item in form.match_rate_roleset.data.split(',') if item.strip()]
            character.set_match_rate_roleset_list(roleset_list)
        else:
            character.match_rate_roleset = None

        # Handle attributes JSON
        if form.attributes.data:
            try:
                json.loads(form.attributes.data)  # Validate JSON
                character.attributes = form.attributes.data
            except json.JSONDecodeError:
                flash('Invalid JSON format in attributes', 'danger')
                return render_template('characters/edit_enhanced.html', form=form, character=character)
        else:
            character.attributes = None
        
        # Handle new file upload
        if form.template_image.data:
            file = form.template_image.data
            if allowed_file(file.filename):
                # Delete old files
                if character.template_image:
                    old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'characters', character.template_image)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)
                
                if character.thumbnail:
                    old_thumb_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', character.thumbnail)
                    if os.path.exists(old_thumb_path):
                        os.remove(old_thumb_path)
                
                # Save new file
                filename = generate_unique_filename(secure_filename(file.filename))
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'characters', filename)
                file.save(file_path)
                
                # Create thumbnail
                thumbnail_filename = f"thumb_{filename}"
                thumbnail_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', thumbnail_filename)
                
                if create_thumbnail(file_path, thumbnail_path):
                    character.thumbnail = thumbnail_filename
                
                character.template_image = filename
        
        # Update tags
        character.tags.clear()
        if form.tags.data:
            selected_tags = Tag.query.filter(Tag.id.in_(form.tags.data)).all()
            character.tags.extend(selected_tags)
        
        db.session.commit()
        flash('Character updated successfully!', 'success')
        return redirect(url_for('characters.view', id=character.id))
    
    return render_template('characters/edit_enhanced.html', form=form, character=character)

@characters.route('/<int:id>/delete', methods=['POST'])
@owner_or_admin_required(lambda id: check_character_ownership(id))
def delete(id):
    """Delete character"""
    character = Character.query.get_or_404(id)
    log_access_attempt(f"characters.delete.{id}", True, f"User {current_user.username} deleting character {character.name}")
    
    # Delete associated files
    if character.template_image:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'characters', character.template_image)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    if character.thumbnail:
        thumb_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', character.thumbnail)
        if os.path.exists(thumb_path):
            os.remove(thumb_path)
    
    db.session.delete(character)
    db.session.commit()
    flash('Character deleted successfully!', 'success')
    return redirect(url_for('characters.index'))

@characters.route('/<int:id>/debug-edit')
@login_required_with_message("Please log in to debug character editing.", "info")
def debug_edit(id):
    """Debug route to check edit button visibility"""
    character = Character.query.get_or_404(id)

    from app.utils.auth_utils import can_edit_character, can_delete_character

    debug_info = {
        'character_id': character.id,
        'character_name': character.name,
        'character_creator_id': character.created_by,
        'current_user_id': current_user.id if current_user.is_authenticated else None,
        'current_user_username': current_user.username if current_user.is_authenticated else None,
        'current_user_is_admin': current_user.is_admin if current_user.is_authenticated else False,
        'current_user_authenticated': current_user.is_authenticated,
        'can_edit_result': can_edit_character(character),
        'can_delete_result': can_delete_character(character),
        'creator_matches': character.created_by == current_user.id if current_user.is_authenticated else False
    }

    return jsonify(debug_info)

@characters.route('/<int:id>/qdrant-images')
@login_required_with_message("Please log in to view character images.", "info")
def qdrant_images(id):
    """Get Qdrant images for character with filtering"""
    character = Character.query.get_or_404(id)

    if not qdrant_service.is_enabled():
        return jsonify({'error': 'Qdrant service not enabled'}), 400

    # Get filter parameters
    image_type = request.args.get('type', '')
    nsfw_filter = request.args.get('nsfw')
    scenario_mode = request.args.get('scenario_mode')
    limit = min(int(request.args.get('limit', 50)), 100)  # Max 100 images

    # Build filters
    filters = {}
    if image_type:
        filters['type'] = image_type
    if nsfw_filter is not None:
        filters['type_nsfw'] = nsfw_filter.lower() == 'true'
    if scenario_mode is not None:
        filters['scenario_mode'] = scenario_mode.lower() == 'true'

    try:
        # Get character ID for Qdrant search
        character_id = getattr(character, 'character_id', None) or str(character.id)

        # Get images from Qdrant
        images = qdrant_service.get_images_by_character_id(character_id, limit=limit, filters=filters)

        # Get total count
        total_count = qdrant_service.get_character_image_count(character_id)

        return jsonify({
            'success': True,
            'images': images,
            'total_count': total_count,
            'displayed_count': len(images),
            'character_id': character_id,
            'filters': filters
        })

    except Exception as e:
        current_app.logger.error(f"Error retrieving Qdrant images for character {character.id}: {e}")
        return jsonify({'error': str(e)}), 500
