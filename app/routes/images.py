import os
import json
from flask import Blueprint, render_template, request, flash, redirect, url_for, current_app, send_from_directory
from flask_login import current_user
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, SubmitField, SelectField, BooleanField
from wtforms.validators import DataRequired
from werkzeug.utils import secure_filename
from app.models import GeneratedImage, Character
from app.utils.image_utils import allowed_file, generate_unique_filename, create_thumbnail, get_image_info, optimize_image
from app.utils.auth_utils import (
    login_required_with_message,
    owner_or_admin_required,
    check_image_ownership,
    log_access_attempt
)
from app.services import qdrant_service
from app.services.gemini_service import LlamaIndexQdrant
from app import db
import asyncio
from dotenv import load_dotenv
load_dotenv()

images = Blueprint('images', __name__)

class ImageUploadForm(FlaskForm):
    character_id = SelectField('Character', coerce=str, validators=[DataRequired()])  # Changed to string
    image_file = FileField('Image File', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif', 'webp'], 'Images only!')
    ])
    description = TextAreaField('Description', validators=[DataRequired(message="Description is required")])
    generation_parameters = TextAreaField('Generation Parameters (JSON format)')
    scenario_mode = BooleanField('Scenario Mode', default=False)
    submit = SubmitField('Upload Image')

@images.route('/')
@login_required_with_message("Please log in to browse images.", "info")
def index():
    """List all generated images with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    character_filter = request.args.get('character', type=int)
    search = request.args.get('search', '').strip()

    query = GeneratedImage.query

    # Apply filters
    if character_filter:
        query = query.filter(GeneratedImage.character_id == character_filter)

    if search:
        query = query.filter(GeneratedImage.description.contains(search) |
                           GeneratedImage.original_filename.contains(search))

    images_pagination = query.order_by(GeneratedImage.created_at.desc()).paginate(
        page=page, per_page=current_app.config['IMAGES_PER_PAGE'],
        error_out=False
    )

    # Get all characters for filter dropdown
    all_characters = Character.query.all()

    return render_template('images/index.html',
                         images=images_pagination.items,
                         pagination=images_pagination,
                         all_characters=all_characters,
                         current_character=character_filter,
                         search_query=search)

@images.route('/qdrant')
@login_required_with_message("Please log in to browse vector images.", "info")
def qdrant_index():
    """List all images from Qdrant vector database with pagination and filtering"""
    if not qdrant_service.is_enabled():
        flash('Vector database is not enabled.', 'warning')
        return redirect(url_for('images.index'))

    page = request.args.get('page', 1, type=int)
    character_filter = request.args.get('character', '').strip()
    nsfw_filter = request.args.get('nsfw', '').strip()
    per_page = 24  # Images per page

    try:
        # Get all character IDs from Qdrant for filtering
        all_character_ids = qdrant_service.get_all_character_ids()

        # Get all images from Qdrant with pagination simulation
        all_images = []

        if character_filter:
            # Get images for specific character
            images = qdrant_service.get_images_by_character_id(character_filter, limit=1000)
            all_images.extend(images)
        else:
            # Get images for all characters
            for char_id in all_character_ids:
                images = qdrant_service.get_images_by_character_id(char_id, limit=100)
                all_images.extend(images)

        # Apply additional filters
        filtered_images = all_images

        if nsfw_filter:
            nsfw_bool = nsfw_filter.lower() == 'true'
            filtered_images = [img for img in filtered_images
                             if img.get('metadata', {}).get('type_nsfw') == nsfw_bool]

        # Sort by timestamp (newest first)
        filtered_images.sort(key=lambda x: x.get('metadata', {}).get('timestamp', ''), reverse=True)

        # Manual pagination
        total_images = len(filtered_images)
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        page_images = filtered_images[start_idx:end_idx]

        # Create pagination info
        total_pages = (total_images + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages

        pagination_info = {
            'page': page,
            'per_page': per_page,
            'total': total_images,
            'pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None
        }

        # Get character names mapping
        character_names = {}
        for char in Character.query.all():
            char_id = getattr(char, 'character_id', None) or str(char.id)
            character_names[char_id] = char.name

        return render_template('images/qdrant_index.html',
                             images=page_images,
                             pagination=pagination_info,
                             all_character_ids=all_character_ids,
                             character_names=character_names,
                             current_character=character_filter,
                             current_nsfw=nsfw_filter,
                             total_images=total_images)

    except Exception as e:
        current_app.logger.error(f"Error retrieving Qdrant images: {e}")
        flash(f'Error retrieving vector images: {str(e)}', 'danger')
        return redirect(url_for('images.index'))

@images.route('/upload', methods=['GET', 'POST'])
@login_required_with_message("Please log in to upload images.", "info")
def upload():
    """Upload a new generated image"""
    form = ImageUploadForm()

    # Get characters from MongoDB
    from app.services import mongodb_service
    try:
        characters = mongodb_service.get_all_characters()
        form.character_id.choices = [(char.get('character_id', ''), char.get('name', 'Unknown')) for char in characters]
    except Exception as e:
        current_app.logger.error(f"Error loading characters from MongoDB: {e}")
        form.character_id.choices = []
        flash('Error loading characters. Please try again.', 'danger')
    
    if form.validate_on_submit():
        file = form.image_file.data
        
        if file and allowed_file(file.filename):
            # Generate unique filename
            filename = generate_unique_filename(secure_filename(file.filename))
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'generated', filename)
            
            # Save file
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            file.save(file_path)
            
            # Optimize image
            optimize_image(file_path)
            
            # Get image info
            width, height, file_size = get_image_info(file_path)
            
            # Create thumbnail
            thumbnail_filename = f"thumb_{filename}"
            thumbnail_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', thumbnail_filename)
            create_thumbnail(file_path, thumbnail_path)
            
            # Create database record
            image = GeneratedImage(
                filename=filename,
                original_filename=file.filename,
                thumbnail=thumbnail_filename,
                file_size=file_size,
                width=width,
                height=height,
                description=form.description.data,
                character_id=form.character_id.data,
                uploaded_by=current_user.id
            )
            
            # Handle generation parameters JSON
            prompt_image = {}
            if form.generation_parameters.data:
                try:
                    prompt_image = json.loads(form.generation_parameters.data)  # Validate JSON
                    image.generation_parameters = form.generation_parameters.data
                except json.JSONDecodeError:
                    flash('Invalid JSON format in generation parameters', 'danger')
                    return render_template('images/upload.html', form=form)

            db.session.add(image)
            db.session.commit()

            # Handle scenario mode - upload to Gemini service
            if form.scenario_mode.data:
                try:
                    # Check if required environment variables are set
                    qdrant_url = current_app.config.get('QDRANT_URL')
                    qdrant_collection = current_app.config.get('QDRANT_COLLECTION')

                    if not qdrant_url:
                        flash('Image uploaded successfully, but Scenario Mode is not configured (missing QDRANT_URL).', 'warning')
                    elif not qdrant_collection:
                        flash('Image uploaded successfully, but Scenario Mode is not configured (missing QDRANT_COLLECTION).', 'warning')
                    else:
                        # Initialize Gemini service
                        gemini_service = LlamaIndexQdrant(
                            url_qdrant=qdrant_url,
                            collection_qdrant=qdrant_collection
                        )

                        # Process upload asynchronously
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(
                            gemini_service.process_upload(
                                character_id=str(form.character_id.data),
                                description=form.description.data,
                                path_file=file_path,
                                prompt_image=prompt_image,
                                scenario_mode=True
                            )
                        )
                        loop.close()

                        if result:
                            flash('Image uploaded successfully and processed with Scenario Mode!', 'success')
                        else:
                            flash('Image uploaded successfully, but Scenario Mode processing failed.', 'warning')

                except Exception as e:
                    current_app.logger.error(f"Scenario mode processing error: {e}")
                    flash(f'Image uploaded successfully, but Scenario Mode processing failed: {str(e)}', 'warning')
            else:
                flash('Image uploaded successfully!', 'success')

            return redirect(url_for('images.view', id=image.id))
        else:
            flash('Invalid file type', 'danger')
    
    return render_template('images/upload.html', form=form)

@images.route('/<int:id>')
@login_required_with_message("Please log in to view image details.", "info")
def view(id):
    """View image details"""
    image = GeneratedImage.query.get_or_404(id)

    # Get character data from MongoDB
    from app.services import mongodb_service
    character = None
    try:
        character = mongodb_service.get_character_by_id(image.character_id)
    except Exception as e:
        current_app.logger.error(f"Error loading character from MongoDB: {e}")

    # Get related images from the same character (excluding current image)
    related_images = GeneratedImage.query.filter(
        GeneratedImage.character_id == image.character_id,
        GeneratedImage.id != image.id
    ).limit(4).all()

    # Get total count of images for this character
    total_images_count = GeneratedImage.query.filter(
        GeneratedImage.character_id == image.character_id
    ).count()

    return render_template('images/view.html',
                         image=image,
                         character=character,
                         related_images=related_images,
                         total_images_count=total_images_count)

@images.route('/<int:id>/edit', methods=['GET', 'POST'])
@owner_or_admin_required(lambda id: check_image_ownership(id))
def edit(id):
    """Edit image details"""
    image = GeneratedImage.query.get_or_404(id)
    log_access_attempt(f"images.edit.{id}", True, f"User {current_user.username} editing image {image.original_filename}")
    
    form = ImageUploadForm(obj=image)

    # Get characters from MongoDB
    from app.services import mongodb_service
    try:
        characters = mongodb_service.get_all_characters()
        form.character_id.choices = [(char.get('character_id', ''), char.get('name', 'Unknown')) for char in characters]
    except Exception as e:
        current_app.logger.error(f"Error loading characters from MongoDB: {e}")
        form.character_id.choices = []
        flash('Error loading characters. Please try again.', 'danger')

    form.image_file.validators = []  # Remove required validator for edit
    
    if request.method == 'GET':
        form.character_id.data = image.character_id
    
    if form.validate_on_submit():
        image.description = form.description.data
        image.character_id = form.character_id.data
        
        # Handle generation parameters JSON
        if form.generation_parameters.data:
            try:
                json.loads(form.generation_parameters.data)  # Validate JSON
                image.generation_parameters = form.generation_parameters.data
            except json.JSONDecodeError:
                flash('Invalid JSON format in generation parameters', 'danger')
                return render_template('images/edit.html', form=form, image=image)
        else:
            image.generation_parameters = None
        
        db.session.commit()
        flash('Image updated successfully!', 'success')
        return redirect(url_for('images.view', id=image.id))
    
    return render_template('images/edit.html', form=form, image=image)

@images.route('/<int:id>/delete', methods=['POST'])
@owner_or_admin_required(lambda id: check_image_ownership(id))
def delete(id):
    """Delete image"""
    image = GeneratedImage.query.get_or_404(id)
    log_access_attempt(f"images.delete.{id}", True, f"User {current_user.username} deleting image {image.original_filename}")
    
    # Delete files
    if image.filename:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'generated', image.filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    if image.thumbnail:
        thumb_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', image.thumbnail)
        if os.path.exists(thumb_path):
            os.remove(thumb_path)
    
    db.session.delete(image)
    db.session.commit()
    flash('Image deleted successfully!', 'success')
    return redirect(url_for('images.index'))

@images.route('/serve/<path:filename>')
@login_required_with_message("Please log in to view images.", "info")
def serve_image(filename):
    """Serve uploaded images"""
    # Determine which folder to serve from
    if filename.startswith('thumb_'):
        return send_from_directory(os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails'), filename)
    elif 'generated' in request.args:
        return send_from_directory(os.path.join(current_app.config['UPLOAD_FOLDER'], 'generated'), filename)
    else:
        return send_from_directory(os.path.join(current_app.config['UPLOAD_FOLDER'], 'characters'), filename)

@images.route('/bulk-upload', methods=['GET', 'POST'])
@login_required_with_message("Please log in to upload images.", "info")
def bulk_upload():
    """Bulk upload multiple images"""
    if request.method == 'POST':
        character_id = request.form.get('character_id')  # Changed to string
        files = request.files.getlist('files')

        if not character_id:
            flash('Please select a character', 'danger')
            return redirect(url_for('images.bulk_upload'))

        # Verify character exists in MongoDB
        from app.services import mongodb_service
        try:
            character = mongodb_service.get_character_by_id(character_id)
            if not character:
                flash('Character not found', 'danger')
                return redirect(url_for('images.bulk_upload'))
        except Exception as e:
            current_app.logger.error(f"Error verifying character: {e}")
            flash('Error verifying character. Please try again.', 'danger')
            return redirect(url_for('images.bulk_upload'))

        uploaded_count = 0
        
        for file in files:
            if file and allowed_file(file.filename):
                # Generate unique filename
                filename = generate_unique_filename(secure_filename(file.filename))
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'generated', filename)
                
                # Save file
                file.save(file_path)
                
                # Optimize image
                optimize_image(file_path)
                
                # Get image info
                width, height, file_size = get_image_info(file_path)
                
                # Create thumbnail
                thumbnail_filename = f"thumb_{filename}"
                thumbnail_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', thumbnail_filename)
                create_thumbnail(file_path, thumbnail_path)
                
                # Create database record
                image = GeneratedImage(
                    filename=filename,
                    original_filename=file.filename,
                    thumbnail=thumbnail_filename,
                    file_size=file_size,
                    width=width,
                    height=height,
                    character_id=character_id,
                    uploaded_by=current_user.id
                )
                
                db.session.add(image)
                uploaded_count += 1
        
        db.session.commit()
        flash(f'Successfully uploaded {uploaded_count} images!', 'success')
        return redirect(url_for('images.index'))  # Redirect to images index instead

    # Get characters from MongoDB
    from app.services import mongodb_service
    try:
        characters = mongodb_service.get_all_characters()
    except Exception as e:
        current_app.logger.error(f"Error loading characters from MongoDB: {e}")
        characters = []
        flash('Error loading characters. Please try again.', 'danger')

    return render_template('images/bulk_upload.html', characters=characters)
