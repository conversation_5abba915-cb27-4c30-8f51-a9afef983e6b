import os
import json
from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user
from werkzeug.utils import secure_filename
from app.models import Character, GeneratedImage, Tag, User
from app.utils.image_utils import allowed_file, generate_unique_filename, create_thumbnail, get_image_info, optimize_image
from app.utils.auth_utils import (
    api_login_required,
    api_admin_required,
    api_owner_or_admin_required,
    check_character_ownership,
    check_image_ownership
)
from app.services import qdrant_service
from app import db

api = Blueprint('api', __name__)

# API endpoints are CSRF exempt via blueprint configuration

@api.route('/characters')
@api_login_required
def get_characters():
    """Get all characters with pagination (includes MongoDB characters via automatic sync)"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    include_mongodb = request.args.get('include_mongodb', 'true').lower() == 'true'

    # Automatic MongoDB sync if enabled and requested
    if include_mongodb:
        try:
            from app.services import mongodb_service, character_sync_service

            if mongodb_service.is_enabled():
                # Check if we need to sync (only if MongoDB has more recent data)
                mongo_characters = mongodb_service.get_all_characters()
                if mongo_characters:
                    # Perform a quick sync to ensure we have the latest data
                    character_sync_service.sync_from_mongodb(force_update=False)
        except Exception as e:
            # Log error but continue with SQLite data
            current_app.logger.warning(f"MongoDB sync error in API: {e}")

    # Query SQLite characters (now includes synced MongoDB characters)
    characters = Character.query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'characters': [{
            'id': c.id,
            'character_id': getattr(c, 'character_id', None),
            'name': c.name,
            'description': c.description,
            'thumbnail': c.thumbnail,
            'created_at': c.created_at.isoformat(),
            'tags': [{'id': t.id, 'name': t.name, 'color': t.color} for t in c.tags],
            'image_count': c.get_generated_images_count(),
            'mongodb_fields': {
                'age': getattr(c, 'age', None),
                'occupation': getattr(c, 'occupation', None),
                'hair_color': getattr(c, 'hair_color', None),
                'eye_color': getattr(c, 'eye_color', None),
                'gender': getattr(c, 'gender', None),
                'timezone': getattr(c, 'timezone', None),
                'bio': getattr(c, 'bio', None)
            } if hasattr(c, 'character_id') else None
        } for c in characters.items],
        'pagination': {
            'page': characters.page,
            'pages': characters.pages,
            'per_page': characters.per_page,
            'total': characters.total,
            'has_next': characters.has_next,
            'has_prev': characters.has_prev
        },
        'mongodb_enabled': mongodb_service.is_enabled() if 'mongodb_service' in locals() else False,
        'sync_performed': include_mongodb and 'mongodb_service' in locals() and mongodb_service.is_enabled()
    })

@api.route('/characters/<int:id>')
@api_login_required
def get_character(id):
    """Get character details"""
    character = Character.query.get_or_404(id)

    return jsonify({
        'id': character.id,
        'name': character.name,
        'description': character.description,
        'attributes': character.attributes,
        'template_image': character.template_image,
        'thumbnail': character.thumbnail,
        'created_at': character.created_at.isoformat(),
        'updated_at': character.updated_at.isoformat(),
        'creator': {
            'id': character.creator.id,
            'username': character.creator.username
        },
        'tags': [{'id': t.id, 'name': t.name, 'color': t.color} for t in character.tags],
        'generated_images': [{
            'id': img.id,
            'filename': img.filename,
            'thumbnail': img.thumbnail,
            'description': img.description,
            'created_at': img.created_at.isoformat()
        } for img in GeneratedImage.query.filter_by(character_id=character.character_id).limit(10)]
    })

@api.route('/characters', methods=['POST'])
@api_login_required
def create_character():
    """Create a new character"""
    data = request.get_json()

    if not data or 'name' not in data:
        return jsonify({'error': 'Character name is required'}), 400

    # Validate JSON attributes if provided
    if 'attributes' in data and data['attributes']:
        try:
            json.loads(data['attributes'])
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON format in attributes'}), 400

    character = Character(
        name=data['name'],
        description=data.get('description', ''),
        attributes=data.get('attributes'),
        created_by=current_user.id
    )

    db.session.add(character)
    db.session.flush()  # Get the character ID

    # Add tags if provided
    if 'tag_ids' in data and data['tag_ids']:
        tags = Tag.query.filter(Tag.id.in_(data['tag_ids'])).all()
        character.tags.extend(tags)

    db.session.commit()

    return jsonify({
        'id': character.id,
        'name': character.name,
        'description': character.description,
        'attributes': character.attributes,
        'created_at': character.created_at.isoformat(),
        'creator': {
            'id': character.creator.id,
            'username': character.creator.username
        },
        'tags': [{'id': t.id, 'name': t.name, 'color': t.color} for t in character.tags]
    }), 201

@api.route('/characters/<int:id>', methods=['PUT'])
@api_login_required
def update_character(id):
    """Update character"""
    character = Character.query.get_or_404(id)

    # Permission check is handled by decorator

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    # Validate JSON attributes if provided
    if 'attributes' in data and data['attributes']:
        try:
            json.loads(data['attributes'])
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON format in attributes'}), 400

    # Update fields
    if 'name' in data:
        character.name = data['name']
    if 'description' in data:
        character.description = data['description']
    if 'attributes' in data:
        character.attributes = data['attributes']

    # Update tags
    if 'tag_ids' in data:
        character.tags.clear()
        if data['tag_ids']:
            tags = Tag.query.filter(Tag.id.in_(data['tag_ids'])).all()
            character.tags.extend(tags)

    db.session.commit()

    return jsonify({
        'id': character.id,
        'name': character.name,
        'description': character.description,
        'attributes': character.attributes,
        'updated_at': character.updated_at.isoformat(),
        'tags': [{'id': t.id, 'name': t.name, 'color': t.color} for t in character.tags]
    })

@api.route('/characters/<int:id>', methods=['DELETE'])
@api_owner_or_admin_required(lambda id: check_character_ownership(id))
def delete_character(id):
    """Delete character"""
    character = Character.query.get_or_404(id)

    # Check permissions
    if character.created_by != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'Permission denied'}), 403

    # Delete associated files
    if character.template_image:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'characters', character.template_image)
        if os.path.exists(file_path):
            os.remove(file_path)

    if character.thumbnail:
        thumb_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', character.thumbnail)
        if os.path.exists(thumb_path):
            os.remove(thumb_path)

    db.session.delete(character)
    db.session.commit()

    return jsonify({'message': 'Character deleted successfully'})

@api.route('/images')
@api_login_required
def get_images():
    """Get all generated images with pagination"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    character_id = request.args.get('character_id', type=int)
    
    query = GeneratedImage.query
    if character_id:
        query = query.filter(GeneratedImage.character_id == character_id)
    
    images = query.order_by(GeneratedImage.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'images': [{
            'id': img.id,
            'filename': img.filename,
            'original_filename': img.original_filename,
            'thumbnail': img.thumbnail,
            'description': img.description,
            'width': img.width,
            'height': img.height,
            'file_size': img.file_size,
            'created_at': img.created_at.isoformat(),
            'character': {
                'character_id': img.character_id,
                'name': img.character_id  # Will be enhanced with MongoDB data if needed
            },
            'uploader': {
                'id': img.uploader.id,
                'username': img.uploader.username
            }
        } for img in images.items],
        'pagination': {
            'page': images.page,
            'pages': images.pages,
            'per_page': images.per_page,
            'total': images.total,
            'has_next': images.has_next,
            'has_prev': images.has_prev
        }
    })

@api.route('/images/<int:id>')
@api_login_required
def get_image(id):
    """Get image details"""
    image = GeneratedImage.query.get_or_404(id)

    return jsonify({
        'id': image.id,
        'filename': image.filename,
        'original_filename': image.original_filename,
        'thumbnail': image.thumbnail,
        'description': image.description,
        'width': image.width,
        'height': image.height,
        'file_size': image.file_size,
        'generation_parameters': image.generation_parameters,
        'created_at': image.created_at.isoformat(),
        'character': {
            'character_id': image.character_id,
            'name': image.character_id,  # Will be enhanced with MongoDB data if needed
            'description': None
        },
        'uploader': {
            'id': image.uploader.id,
            'username': image.uploader.username
        }
    })

@api.route('/images', methods=['POST'])
@api_login_required
def upload_image():
    """Upload a new image"""
    if 'image_file' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400

    file = request.files['image_file']
    character_id = request.form.get('character_id', type=int)
    description = request.form.get('description', '')
    generation_parameters = request.form.get('generation_parameters', '')

    if not character_id:
        return jsonify({'error': 'Character ID is required'}), 400

    Character.query.get_or_404(character_id)  # Validate character exists

    if not file or file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type'}), 400

    # Validate generation parameters JSON if provided
    if generation_parameters:
        try:
            json.loads(generation_parameters)
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON format in generation parameters'}), 400

    # Generate unique filename and save file
    filename = generate_unique_filename(secure_filename(file.filename))
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'generated', filename)

    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    file.save(file_path)

    # Optimize image
    optimize_image(file_path)

    # Get image info
    width, height, file_size = get_image_info(file_path)

    # Create thumbnail
    thumbnail_filename = f"thumb_{filename}"
    thumbnail_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', thumbnail_filename)
    create_thumbnail(file_path, thumbnail_path)

    # Create database record
    image = GeneratedImage(
        filename=filename,
        original_filename=file.filename,
        thumbnail=thumbnail_filename,
        file_size=file_size,
        width=width,
        height=height,
        description=description,
        generation_parameters=generation_parameters if generation_parameters else None,
        character_id=character_id,
        uploaded_by=current_user.id
    )

    db.session.add(image)
    db.session.commit()

    return jsonify({
        'id': image.id,
        'filename': image.filename,
        'original_filename': image.original_filename,
        'thumbnail': image.thumbnail,
        'description': image.description,
        'width': image.width,
        'height': image.height,
        'file_size': image.file_size,
        'created_at': image.created_at.isoformat(),
        'character': {
            'character_id': image.character_id,
            'name': image.character_id  # Will be enhanced with MongoDB data if needed
        }
    }), 201

@api.route('/images/<int:id>', methods=['PUT'])
@api_owner_or_admin_required(lambda id: check_image_ownership(id))
def update_image(id):
    """Update image details"""
    image = GeneratedImage.query.get_or_404(id)

    # Check permissions
    if image.uploaded_by != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'Permission denied'}), 403

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    # Validate generation parameters JSON if provided
    if 'generation_parameters' in data and data['generation_parameters']:
        try:
            json.loads(data['generation_parameters'])
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON format in generation parameters'}), 400

    # Update fields
    if 'description' in data:
        image.description = data['description']
    if 'character_id' in data:
        Character.query.get_or_404(data['character_id'])  # Validate character exists
        image.character_id = data['character_id']
    if 'generation_parameters' in data:
        image.generation_parameters = data['generation_parameters']

    db.session.commit()

    return jsonify({
        'id': image.id,
        'description': image.description,
        'generation_parameters': image.generation_parameters,
        'character': {
            'character_id': image.character_id,
            'name': image.character_id  # Will be enhanced with MongoDB data if needed
        }
    })

@api.route('/images/<int:id>', methods=['DELETE'])
@api_owner_or_admin_required(lambda id: check_image_ownership(id))
def delete_image(id):
    """Delete image"""
    image = GeneratedImage.query.get_or_404(id)

    # Check permissions
    if image.uploaded_by != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'Permission denied'}), 403

    # Delete files
    if image.filename:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'generated', image.filename)
        if os.path.exists(file_path):
            os.remove(file_path)

    if image.thumbnail:
        thumb_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails', image.thumbnail)
        if os.path.exists(thumb_path):
            os.remove(thumb_path)

    db.session.delete(image)
    db.session.commit()

    return jsonify({'message': 'Image deleted successfully'})

@api.route('/tags')
def get_tags():
    """Get all tags"""
    tags = Tag.query.all()
    
    return jsonify({
        'tags': [{
            'id': tag.id,
            'name': tag.name,
            'color': tag.color,
            'character_count': len(tag.characters)
        } for tag in tags]
    })

@api.route('/tags', methods=['POST'])
@api_admin_required
def create_tag():
    """Create a new tag"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403
    
    data = request.get_json()
    
    if not data or 'name' not in data:
        return jsonify({'error': 'Tag name is required'}), 400
    
    # Check if tag already exists
    existing_tag = Tag.query.filter_by(name=data['name']).first()
    if existing_tag:
        return jsonify({'error': 'Tag already exists'}), 400
    
    tag = Tag(
        name=data['name'],
        color=data.get('color', '#007bff')
    )
    
    db.session.add(tag)
    db.session.commit()
    
    return jsonify({
        'id': tag.id,
        'name': tag.name,
        'color': tag.color,
        'character_count': 0
    }), 201

@api.route('/search')
@api_login_required
def search():
    """Global search API"""
    query = request.args.get('q', '').strip()
    limit = request.args.get('limit', 10, type=int)
    
    if not query:
        return jsonify({'characters': [], 'images': []})
    
    # Search characters
    characters = Character.query.filter(
        Character.name.contains(query) | 
        Character.description.contains(query)
    ).limit(limit).all()
    
    # Search images
    images = GeneratedImage.query.filter(
        GeneratedImage.description.contains(query) |
        GeneratedImage.original_filename.contains(query)
    ).limit(limit).all()
    
    return jsonify({
        'characters': [{
            'id': c.id,
            'name': c.name,
            'description': c.description,
            'thumbnail': c.thumbnail
        } for c in characters],
        'images': [{
            'id': img.id,
            'filename': img.filename,
            'description': img.description,
            'thumbnail': img.thumbnail,
            'character_name': img.character_id  # Will be enhanced with MongoDB data if needed
        } for img in images]
    })

@api.route('/stats')
def get_stats():
    """Get dashboard statistics including MongoDB status"""
    stats = {
        'total_characters': Character.query.count(),
        'total_images': GeneratedImage.query.count(),
        'total_users': User.query.count(),
        'total_tags': Tag.query.count()
    }

    # Add MongoDB statistics if enabled
    try:
        from app.services import mongodb_service
        if mongodb_service.is_enabled():
            mongo_characters = mongodb_service.get_all_characters()
            stats['mongodb_enabled'] = True
            stats['mongodb_characters'] = len(mongo_characters)
            stats['mongodb_connected'] = mongodb_service.test_connection()
        else:
            stats['mongodb_enabled'] = False
            stats['mongodb_characters'] = 0
            stats['mongodb_connected'] = False
    except Exception as e:
        stats['mongodb_enabled'] = False
        stats['mongodb_characters'] = 0
        stats['mongodb_connected'] = False
        stats['mongodb_error'] = str(e)

    return jsonify(stats)

@api.route('/characters/all-sources')
@api_login_required
def get_characters_all_sources():
    """Get characters from both SQLite and MongoDB with source information"""
    try:
        from app.services import mongodb_service

        result = {
            'sqlite_characters': [],
            'mongodb_characters': [],
            'merged_characters': [],
            'mongodb_enabled': mongodb_service.is_enabled(),
            'sync_status': {}
        }

        # Get SQLite characters
        sqlite_chars = Character.query.all()
        result['sqlite_characters'] = [{
            'id': c.id,
            'character_id': getattr(c, 'character_id', None),
            'name': c.name,
            'description': c.description,
            'source': 'sqlite',
            'created_at': c.created_at.isoformat(),
            'mongodb_fields': bool(hasattr(c, 'character_id') and getattr(c, 'character_id'))
        } for c in sqlite_chars]

        # Get MongoDB characters if enabled
        if mongodb_service.is_enabled():
            try:
                mongo_chars = mongodb_service.get_all_characters()
                result['mongodb_characters'] = [{
                    'character_id': char.get('character_id'),
                    'name': char.get('name'),
                    'age': char.get('age'),
                    'occupation': char.get('occupation'),
                    'source': 'mongodb',
                    'in_sqlite': any(sc.get('character_id') == char.get('character_id')
                                   for sc in result['sqlite_characters'])
                } for char in mongo_chars]

                # Create merged view
                merged = {}

                # Add SQLite characters
                for char in result['sqlite_characters']:
                    char_id = char.get('character_id') or f"sqlite_{char['id']}"
                    merged[char_id] = {
                        **char,
                        'sources': ['sqlite']
                    }

                # Add/merge MongoDB characters
                for char in result['mongodb_characters']:
                    char_id = char.get('character_id')
                    if char_id:
                        if char_id in merged:
                            merged[char_id]['sources'].append('mongodb')
                            merged[char_id]['mongodb_data'] = char
                        else:
                            merged[char_id] = {
                                **char,
                                'sources': ['mongodb']
                            }

                result['merged_characters'] = list(merged.values())
                result['sync_status'] = {
                    'sqlite_only': len([c for c in merged.values() if c['sources'] == ['sqlite']]),
                    'mongodb_only': len([c for c in merged.values() if c['sources'] == ['mongodb']]),
                    'both_sources': len([c for c in merged.values() if len(c['sources']) > 1])
                }

            except Exception as e:
                result['mongodb_error'] = str(e)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'error': str(e),
            'sqlite_characters': [],
            'mongodb_characters': [],
            'merged_characters': []
        }), 500

# Qdrant Image Management Endpoints

@api.route('/qdrant/images')
@api_login_required
def get_qdrant_images():
    """Get all images from Qdrant vector database"""
    if not qdrant_service.is_enabled():
        return jsonify({'error': 'Vector database not enabled'}), 503

    try:
        character_filter = request.args.get('character_id', '').strip()
        type_filter = request.args.get('type', '').strip()
        nsfw_filter = request.args.get('nsfw', '').strip()
        limit = request.args.get('limit', 100, type=int)

        # Get all character IDs from Qdrant
        all_character_ids = qdrant_service.get_all_character_ids()

        # Get images
        all_images = []

        if character_filter:
            images = qdrant_service.get_images_by_character_id(character_filter, limit=limit)
            all_images.extend(images)
        else:
            # Get images for all characters (limited)
            for char_id in all_character_ids[:20]:  # Limit to first 20 characters for performance
                images = qdrant_service.get_images_by_character_id(char_id, limit=50)
                all_images.extend(images)

        # Apply filters
        if type_filter:
            all_images = [img for img in all_images
                         if img.get('metadata', {}).get('type') == type_filter]

        if nsfw_filter:
            nsfw_bool = nsfw_filter.lower() == 'true'
            all_images = [img for img in all_images
                         if img.get('metadata', {}).get('type_nsfw') == nsfw_bool]

        return jsonify({
            'images': all_images,
            'total_count': len(all_images),
            'character_ids': all_character_ids
        })

    except Exception as e:
        current_app.logger.error(f"Error retrieving Qdrant images: {e}")
        return jsonify({'error': str(e)}), 500

@api.route('/qdrant/images/<string:image_id>')
@api_login_required
def get_qdrant_image(image_id):
    """Get specific image from Qdrant by ID"""
    if not qdrant_service.is_enabled():
        return jsonify({'error': 'Vector database not enabled'}), 503

    try:
        image = qdrant_service.get_image_by_id(image_id)

        if not image:
            return jsonify({'error': 'Image not found'}), 404

        return jsonify(image)

    except Exception as e:
        current_app.logger.error(f"Error retrieving Qdrant image {image_id}: {e}")
        return jsonify({'error': str(e)}), 500

@api.route('/qdrant/images/<string:image_id>', methods=['DELETE'])
@api_login_required
def delete_qdrant_image(image_id):
    """Delete image from Qdrant vector database"""
    if not qdrant_service.is_enabled():
        return jsonify({'error': 'Vector database not enabled'}), 503

    try:
        # Get image details before deletion for permission checking
        image = qdrant_service.get_image_by_id(image_id)

        if not image:
            return jsonify({'error': 'Image not found'}), 404

        # Check permissions: admin or character owner
        metadata = image.get('metadata', {})
        character_id = metadata.get('character_id')

        has_permission = False

        if current_user.is_admin:
            has_permission = True
        elif character_id:
            # Find the character and check ownership
            from app.models import Character
            character = Character.query.filter(
                (Character.character_id == character_id) |
                (Character.id == character_id)
            ).first()

            if character and character.created_by == current_user.id:
                has_permission = True

        if not has_permission:
            return jsonify({'error': 'Permission denied. You can only delete images from your own characters.'}), 403

        # Delete the image
        success = qdrant_service.delete_image(image_id)

        if success:
            current_app.logger.info(f"User {current_user.username} deleted Qdrant image {image_id} for character {character_id}")
            return jsonify({'message': 'Image deleted successfully'})
        else:
            return jsonify({'error': 'Failed to delete image'}), 500

    except Exception as e:
        current_app.logger.error(f"Error deleting Qdrant image {image_id}: {e}")
        return jsonify({'error': str(e)}), 500

@api.route('/characters/<int:id>/field', methods=['PUT'])
@api_login_required
def update_character_field(id):
    """Update a single character field"""
    character = Character.query.get_or_404(id)

    data = request.get_json()
    if not data or 'field' not in data or 'value' not in data:
        return jsonify({'error': 'Field name and value are required'}), 400

    field_name = data['field']
    field_value = data['value']

    # Validate field name
    allowed_fields = [
        'name', 'age', 'occupation', 'country', 'hobbies', 'hair_color',
        'hair_style', 'eye_color', 'eye_type', 'face_detail', 'body_detail',
        'skin_color', 'personality_roleplay', 'style_roleplay', 'nsfw_style',
        'timezone', 'gender', 'types', 'prompt_gen_image', 'prompt_negative_gen_image',
        'match_rate_roleset', 'bio', 'scenario_information', 'prompt_gen_scenario_image',
        'voice_id', 'character_id'
    ]

    if field_name not in allowed_fields:
        return jsonify({'error': f'Field "{field_name}" is not allowed to be updated'}), 400

    try:
        # Handle special field types
        if field_name == 'age' and field_value:
            if isinstance(field_value, str) and field_value.isdigit():
                field_value = int(field_value)
            elif not isinstance(field_value, int):
                return jsonify({'error': 'Age must be a number'}), 400
        elif field_name == 'timezone' and field_value:
            if isinstance(field_value, str) and field_value.lstrip('-').isdigit():
                field_value = int(field_value)
            elif not isinstance(field_value, int):
                return jsonify({'error': 'Timezone must be a number'}), 400

        # Update the field
        setattr(character, field_name, field_value if field_value else None)
        db.session.commit()

        current_app.logger.info(f"User {current_user.username} updated {field_name} for character {character.name}")

        return jsonify({
            'success': True,
            'field': field_name,
            'value': field_value,
            'message': f'{field_name.replace("_", " ").title()} updated successfully'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating character field {field_name}: {e}")
        return jsonify({'error': str(e)}), 500

@api.route('/characters/<int:id>/fields', methods=['PUT'])
@api_login_required
def update_character_fields_bulk(id):
    """Update multiple character fields at once"""
    character = Character.query.get_or_404(id)

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    # Support both formats: {'fields': {...}} and direct field data
    if 'fields' in data:
        fields_data = data['fields']
    else:
        fields_data = data

    # Validate all field names first
    allowed_fields = [
        'name', 'age', 'occupation', 'country', 'hobbies', 'hair_color',
        'hair_style', 'eye_color', 'eye_type', 'face_detail', 'body_detail',
        'skin_color', 'personality_roleplay', 'style_roleplay', 'nsfw_style',
        'timezone', 'gender', 'types', 'prompt_gen_image', 'prompt_negative_gen_image',
        'match_rate_roleset', 'bio', 'scenario_information', 'prompt_gen_scenario_image',
        'voice_id', 'character_id'
    ]

    invalid_fields = [field for field in fields_data.keys() if field not in allowed_fields]
    if invalid_fields:
        return jsonify({'error': f'Invalid fields: {", ".join(invalid_fields)}'}), 400

    # Validate required fields
    if 'name' in fields_data and not fields_data['name']:
        return jsonify({'error': 'Character name cannot be empty'}), 400

    try:
        updated_fields = []
        failed_fields = []

        for field_name, field_value in fields_data.items():
            try:
                # Sanitize and validate field values
                if field_value is not None and isinstance(field_value, str):
                    field_value = field_value.strip()

                # Handle special field types with validation
                if field_name == 'age':
                    if field_value:
                        if isinstance(field_value, str) and field_value.isdigit():
                            field_value = int(field_value)
                        elif not isinstance(field_value, int):
                            failed_fields.append({'field': field_name, 'error': 'Age must be a number'})
                            continue
                        if field_value < 1 or field_value > 999:
                            failed_fields.append({'field': field_name, 'error': 'Age must be between 1 and 999'})
                            continue
                    else:
                        field_value = None

                elif field_name == 'timezone':
                    if field_value:
                        if isinstance(field_value, str) and field_value.lstrip('-').isdigit():
                            field_value = int(field_value)
                        elif not isinstance(field_value, int):
                            failed_fields.append({'field': field_name, 'error': 'Timezone must be a number'})
                            continue
                        if field_value < -12 or field_value > 14:
                            failed_fields.append({'field': field_name, 'error': 'Timezone must be between -12 and +14'})
                            continue
                    else:
                        field_value = None

                elif field_name == 'name':
                    if not field_value:
                        failed_fields.append({'field': field_name, 'error': 'Name cannot be empty'})
                        continue
                    if len(field_value) > 100:
                        failed_fields.append({'field': field_name, 'error': 'Name cannot exceed 100 characters'})
                        continue

                elif field_name in ['gender', 'types']:
                    # Validate select field values
                    if field_name == 'gender' and field_value:
                        valid_genders = ['Male', 'Female', 'Non-binary', 'Other']
                        if field_value not in valid_genders:
                            failed_fields.append({'field': field_name, 'error': f'Gender must be one of: {", ".join(valid_genders)}'})
                            continue
                    elif field_name == 'types' and field_value:
                        valid_types = ['anime', 'realistic', 'fantasy']
                        if field_value not in valid_types:
                            failed_fields.append({'field': field_name, 'error': f'Type must be one of: {", ".join(valid_types)}'})
                            continue

                elif field_name in ['character_id', 'voice_id']:
                    # Validate ID fields
                    if field_value and len(field_value) > 50:
                        failed_fields.append({'field': field_name, 'error': f'{field_name.replace("_", " ").title()} cannot exceed 50 characters'})
                        continue

                # Validate text field lengths
                text_field_limits = {
                    'occupation': 100,
                    'country': 100,
                    'hobbies': 500,
                    'hair_color': 50,
                    'hair_style': 50,
                    'eye_color': 50,
                    'eye_type': 50,
                    'skin_color': 50,
                    'face_detail': 1000,
                    'body_detail': 1000,
                    'personality_roleplay': 2000,
                    'style_roleplay': 1000,
                    'nsfw_style': 1000,
                    'bio': 2000,
                    'scenario_information': 2000,
                    'prompt_gen_image': 2000,
                    'prompt_negative_gen_image': 1000,
                    'prompt_gen_scenario_image': 2000,
                    'match_rate_roleset': 500
                }

                if field_name in text_field_limits and field_value:
                    if len(field_value) > text_field_limits[field_name]:
                        failed_fields.append({
                            'field': field_name,
                            'error': f'{field_name.replace("_", " ").title()} cannot exceed {text_field_limits[field_name]} characters'
                        })
                        continue

                # Update the field
                setattr(character, field_name, field_value if field_value else None)
                updated_fields.append(field_name)

            except Exception as e:
                failed_fields.append({'field': field_name, 'error': str(e)})

        # Commit all changes if any were successful
        if updated_fields:
            db.session.commit()
            current_app.logger.info(f"User {current_user.username} bulk updated fields {updated_fields} for character {character.name}")

            # Sync to MongoDB if enabled and character has character_id
            try:
                from app.services import mongodb_service, character_sync_service
                if mongodb_service.is_enabled() and hasattr(character, 'character_id') and character.character_id:
                    character_sync_service.sync_character_to_mongodb(character)
                    current_app.logger.info(f"Synced character {character.name} to MongoDB after bulk update")
            except Exception as e:
                current_app.logger.warning(f"Failed to sync character to MongoDB after bulk update: {e}")

        return jsonify({
            'success': len(failed_fields) == 0,
            'updated_fields': updated_fields,
            'failed_fields': failed_fields,
            'message': f'Updated {len(updated_fields)} fields successfully' +
                      (f', {len(failed_fields)} failed' if failed_fields else ''),
            'mongodb_synced': updated_fields and hasattr(character, 'character_id') and character.character_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error bulk updating character fields: {e}")
        return jsonify({'error': str(e)}), 500
