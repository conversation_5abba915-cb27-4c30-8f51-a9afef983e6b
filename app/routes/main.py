from flask import Blueprint, render_template, request
from flask_login import login_required
from sqlalchemy import func
from app.models import Character, GeneratedImage, User, Tag
from app import db

main = Blueprint('main', __name__)

@main.route('/')
def index():
    """Dashboard with statistics and recent activity"""
    # Get statistics
    total_characters = Character.query.count()
    total_images = GeneratedImage.query.count()
    total_users = User.query.count()
    total_tags = Tag.query.count()
    
    # Get recent characters (last 6)
    recent_characters = Character.query.order_by(Character.created_at.desc()).limit(6).all()
    
    # Get recent images (last 8)
    recent_images = GeneratedImage.query.order_by(GeneratedImage.created_at.desc()).limit(8).all()

    # Get character data for recent images
    from app.services import mongodb_service
    characters_data = {}
    try:
        all_characters = mongodb_service.get_all_characters()
        characters_data = {char.get('character_id', ''): char for char in all_characters}
    except Exception as e:
        print(f"Error loading characters from MongoDB: {e}")

    # Get popular tags (top 10 by character count)
    popular_tags = db.session.query(Tag, func.count(Character.id).label('character_count'))\
        .join(Tag.characters)\
        .group_by(Tag.id)\
        .order_by(func.count(Character.id).desc())\
        .limit(10).all()

    stats = {
        'total_characters': total_characters,
        'total_images': total_images,
        'total_users': total_users,
        'total_tags': total_tags
    }

    return render_template('index.html',
                         stats=stats,
                         recent_characters=recent_characters,
                         recent_images=recent_images,
                         characters_data=characters_data,
                         popular_tags=popular_tags)

@main.route('/search')
@login_required
def search():
    """Global search functionality"""
    query = request.args.get('q', '').strip()
    
    if not query:
        return render_template('search.html', query='', characters=[], images=[])
    
    # Search characters
    characters = Character.query.filter(
        Character.name.contains(query) | 
        Character.description.contains(query)
    ).limit(20).all()
    
    # Search images
    images = GeneratedImage.query.filter(
        GeneratedImage.description.contains(query) |
        GeneratedImage.original_filename.contains(query)
    ).limit(20).all()

    # Get character data for images
    from app.services import mongodb_service
    characters_data = {}
    try:
        all_characters = mongodb_service.get_all_characters()
        characters_data = {char.get('character_id', ''): char for char in all_characters}
    except Exception as e:
        print(f"Error loading characters from MongoDB: {e}")

    return render_template('search.html',
                         query=query,
                         characters=characters,
                         images=images,
                         characters_data=characters_data)
