#!/usr/bin/env python3
"""
MongoDB API Routes for Character CMS
Handles MongoDB synchronization and field updates
"""

import json
import logging
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user

from app.services import mongodb_service, character_sync_service
from app.utils.auth_utils import admin_required
from app.models import Character

logger = logging.getLogger(__name__)

mongodb_api = Blueprint('mongodb_api', __name__)

@mongodb_api.route('/status', methods=['GET'])
@login_required
def mongodb_status():
    """Get MongoDB connection status and configuration"""
    try:
        status = {
            'enabled': mongodb_service.is_enabled(),
            'connected': mongodb_service.test_connection() if mongodb_service.is_enabled() else False,
            'database': current_app.config.get('MONGODB_DATABASE', 'character_cms'),
            'collection': current_app.config.get('MONGODB_COLLECTION', 'characters'),
            'pymongo_available': hasattr(mongodb_service, 'client') and mongodb_service.client is not None
        }
        
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"Error getting MongoDB status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/sync/from-mongodb', methods=['POST'])
@admin_required
def sync_from_mongodb():
    """Sync characters from MongoDB to SQLite"""
    try:
        data = request.get_json() or {}
        force_update = data.get('force_update', False)
        
        if not mongodb_service.is_enabled():
            return jsonify({
                'success': False,
                'error': 'MongoDB is not enabled or configured'
            }), 400
        
        # Perform sync
        stats = character_sync_service.sync_from_mongodb(force_update=force_update)
        
        return jsonify({
            'success': True,
            'message': 'Sync from MongoDB completed',
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error syncing from MongoDB: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/sync/to-mongodb', methods=['POST'])
@admin_required
def sync_to_mongodb():
    """Sync characters from SQLite to MongoDB"""
    try:
        data = request.get_json() or {}
        character_ids = data.get('character_ids')  # Optional list of character IDs
        
        if not mongodb_service.is_enabled():
            return jsonify({
                'success': False,
                'error': 'MongoDB is not enabled or configured'
            }), 400
        
        # Perform sync
        stats = character_sync_service.sync_to_mongodb(character_ids=character_ids)
        
        return jsonify({
            'success': True,
            'message': 'Sync to MongoDB completed',
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error syncing to MongoDB: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/characters', methods=['GET'])
@login_required
def get_mongodb_characters():
    """Get all characters from MongoDB"""
    try:
        if not mongodb_service.is_enabled():
            return jsonify({
                'success': False,
                'error': 'MongoDB is not enabled or configured'
            }), 400
        
        characters = mongodb_service.get_all_characters()
        
        # Convert ObjectId to string for JSON serialization
        for char in characters:
            if '_id' in char:
                char['_id'] = str(char['_id'])
        
        return jsonify({
            'success': True,
            'characters': characters,
            'count': len(characters)
        })
        
    except Exception as e:
        logger.error(f"Error getting MongoDB characters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/characters/<character_id>', methods=['GET'])
@login_required
def get_mongodb_character(character_id):
    """Get a specific character from MongoDB"""
    try:
        if not mongodb_service.is_enabled():
            return jsonify({
                'success': False,
                'error': 'MongoDB is not enabled or configured'
            }), 400
        
        character = mongodb_service.get_character_by_id(character_id)
        
        if not character:
            return jsonify({
                'success': False,
                'error': f'Character {character_id} not found in MongoDB'
            }), 404
        
        # Convert ObjectId to string for JSON serialization
        if '_id' in character:
            character['_id'] = str(character['_id'])
        
        return jsonify({
            'success': True,
            'character': character
        })
        
    except Exception as e:
        logger.error(f"Error getting MongoDB character {character_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/characters/<character_id>/field', methods=['PUT'])
@login_required
def update_character_field(character_id):
    """Update a specific field for a character in both SQLite and MongoDB"""
    try:
        data = request.get_json()
        if not data or 'field_name' not in data or 'field_value' not in data:
            return jsonify({
                'success': False,
                'error': 'field_name and field_value are required'
            }), 400
        
        field_name = data['field_name']
        field_value = data['field_value']
        
        # Validate field name
        mongodb_fields = mongodb_service.get_mongodb_fields()
        field_mapping = mongodb_service.get_mongodb_field_mapping()
        
        # Check if it's a valid MongoDB field
        if field_name not in mongodb_fields and field_name not in field_mapping.values():
            return jsonify({
                'success': False,
                'error': f'Invalid field name: {field_name}'
            }), 400
        
        # Check character ownership or admin rights
        character = Character.query.filter_by(character_id=character_id).first()
        if not character:
            return jsonify({
                'success': False,
                'error': f'Character {character_id} not found'
            }), 404
        
        if not current_user.is_admin and character.created_by != current_user.id:
            return jsonify({
                'success': False,
                'error': 'Permission denied'
            }), 403
        
        # Update the field
        success = character_sync_service.update_character_field(
            character_id, field_name, field_value
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Field {field_name} updated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Failed to update field {field_name}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error updating character field: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/fields', methods=['GET'])
@login_required
def get_mongodb_fields():
    """Get list of MongoDB fields that should be displayed in UI"""
    try:
        fields = mongodb_service.get_mongodb_fields()
        field_mapping = mongodb_service.get_mongodb_field_mapping()
        
        return jsonify({
            'success': True,
            'mongodb_fields': fields,
            'field_mapping': field_mapping
        })
        
    except Exception as e:
        logger.error(f"Error getting MongoDB fields: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mongodb_api.route('/test-connection', methods=['POST'])
@admin_required
def test_mongodb_connection():
    """Test MongoDB connection"""
    try:
        if not mongodb_service.is_enabled():
            return jsonify({
                'success': False,
                'error': 'MongoDB is not enabled or configured'
            }), 400
        
        connected = mongodb_service.test_connection()
        
        return jsonify({
            'success': True,
            'connected': connected,
            'message': 'Connection successful' if connected else 'Connection failed'
        })
        
    except Exception as e:
        logger.error(f"Error testing MongoDB connection: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
