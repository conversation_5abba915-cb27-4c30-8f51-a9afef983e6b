from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import current_user
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length
from wtforms.widgets import ColorInput
from app.models import User, Character, GeneratedImage, Tag
from app.utils.auth_utils import admin_required, log_access_attempt
from app.services import mongodb_service
from app import db

admin = Blueprint('admin', __name__)

class TagForm(FlaskForm):
    name = StringField('Tag Name', validators=[DataRequired(), Length(min=1, max=50)])
    color = StringField('Color', widget=ColorInput(), default='#007bff')
    submit = SubmitField('Save Tag')

class UserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('Email', validators=[DataRequired()])
    is_admin = Bo<PERSON>anField('Admin User')
    submit = SubmitField('Save User')

@admin.route('/')
@admin_required
def index():
    """Admin dashboard"""
    # Get statistics
    stats = {
        'total_users': User.query.count(),
        'total_characters': Character.query.count(),
        'total_images': GeneratedImage.query.count(),
        'total_tags': Tag.query.count(),
        'admin_users': User.query.filter_by(is_admin=True).count()
    }
    
    # Get recent activity
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    recent_characters = Character.query.order_by(Character.created_at.desc()).limit(5).all()
    recent_images = GeneratedImage.query.order_by(GeneratedImage.created_at.desc()).limit(5).all()

    # Get character data for recent images
    characters_data = {}
    try:
        all_characters = mongodb_service.get_all_characters()
        characters_data = {char.get('character_id', ''): char for char in all_characters}
    except Exception as e:
        print(f"Error loading characters from MongoDB: {e}")

    return render_template('admin/index.html',
                         stats=stats,
                         recent_users=recent_users,
                         recent_characters=recent_characters,
                         recent_images=recent_images,
                         characters_data=characters_data)

@admin.route('/users')
@admin_required
def users():
    """Manage users"""
    page = request.args.get('page', 1, type=int)
    users_pagination = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/users.html', 
                         users=users_pagination.items,
                         pagination=users_pagination)

@admin.route('/users/<int:id>/toggle-admin', methods=['POST'])
@admin_required
def toggle_user_admin(id):
    """Toggle user admin status"""
    user = User.query.get_or_404(id)
    
    if user.id == current_user.id:
        flash('You cannot change your own admin status.', 'danger')
        return redirect(url_for('admin.users'))
    
    user.is_admin = not user.is_admin
    db.session.commit()
    
    status = 'granted' if user.is_admin else 'revoked'
    flash(f'Admin access {status} for {user.username}.', 'success')
    return redirect(url_for('admin.users'))

@admin.route('/tags')
@admin_required
def tags():
    """Manage tags"""
    tags = Tag.query.order_by(Tag.name).all()
    return render_template('admin/tags.html', tags=tags)

@admin.route('/tags/create', methods=['GET', 'POST'])
@admin_required
def create_tag():
    """Create a new tag"""
    form = TagForm()
    
    if form.validate_on_submit():
        # Check if tag already exists
        existing_tag = Tag.query.filter_by(name=form.name.data).first()
        if existing_tag:
            flash('A tag with this name already exists.', 'danger')
            return render_template('admin/create_tag.html', form=form)
        
        tag = Tag(
            name=form.name.data,
            color=form.color.data
        )
        
        db.session.add(tag)
        db.session.commit()
        flash('Tag created successfully!', 'success')
        return redirect(url_for('admin.tags'))
    
    return render_template('admin/create_tag.html', form=form)

@admin.route('/tags/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_tag(id):
    """Edit a tag"""
    tag = Tag.query.get_or_404(id)
    form = TagForm(obj=tag)
    
    if form.validate_on_submit():
        # Check if another tag with this name exists
        existing_tag = Tag.query.filter(Tag.name == form.name.data, Tag.id != id).first()
        if existing_tag:
            flash('A tag with this name already exists.', 'danger')
            return render_template('admin/edit_tag.html', form=form, tag=tag)
        
        tag.name = form.name.data
        tag.color = form.color.data
        
        db.session.commit()
        flash('Tag updated successfully!', 'success')
        return redirect(url_for('admin.tags'))
    
    return render_template('admin/edit_tag.html', form=form, tag=tag)

@admin.route('/tags/<int:id>/delete', methods=['POST'])
@admin_required
def delete_tag(id):
    """Delete a tag"""
    tag = Tag.query.get_or_404(id)
    
    # Check if tag is in use
    if tag.characters:
        flash(f'Cannot delete tag "{tag.name}" because it is used by {len(tag.characters)} character(s).', 'danger')
        return redirect(url_for('admin.tags'))
    
    db.session.delete(tag)
    db.session.commit()
    flash('Tag deleted successfully!', 'success')
    return redirect(url_for('admin.tags'))

@admin.route('/system-info')
@admin_required
def system_info():
    """Display system information"""
    import os
    import sys
    from datetime import datetime
    
    # Get system info
    info = {
        'python_version': sys.version,
        'flask_version': '3.0.0',  # You can get this dynamically
        'database_url': os.environ.get('DATABASE_URL', 'sqlite:///cms.db'),
        'upload_folder': os.path.abspath('uploads'),
        'debug_mode': os.environ.get('FLASK_ENV') == 'development',
        'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }
    
    # Get disk usage for uploads folder
    try:
        import shutil
        total, used, free = shutil.disk_usage('uploads')
        info['disk_usage'] = {
            'total': total // (1024**3),  # GB
            'used': used // (1024**3),    # GB
            'free': free // (1024**3)     # GB
        }
    except:
        info['disk_usage'] = None
    
    return render_template('admin/system_info.html', info=info)

@admin.route('/mongodb-sync')
@admin_required
def mongodb_sync():
    """MongoDB synchronization interface"""
    return render_template('admin/mongodb_sync.html')

@admin.route('/api/stats')
@admin_required
def api_stats():
    """API endpoint for admin statistics"""
    from sqlalchemy import func
    
    # Character statistics
    char_stats = db.session.query(
        func.count(Character.id).label('total'),
        func.count(Character.template_image).label('with_images')
    ).first()
    
    # Image statistics
    img_stats = db.session.query(
        func.count(GeneratedImage.id).label('total'),
        func.sum(GeneratedImage.file_size).label('total_size')
    ).first()
    
    # User statistics
    user_stats = db.session.query(
        func.count(User.id).label('total'),
        func.count(User.id).filter(User.is_admin == True).label('admins')
    ).first()
    
    return jsonify({
        'characters': {
            'total': char_stats.total or 0,
            'with_images': char_stats.with_images or 0
        },
        'images': {
            'total': img_stats.total or 0,
            'total_size_mb': round((img_stats.total_size or 0) / (1024 * 1024), 2)
        },
        'users': {
            'total': user_stats.total or 0,
            'admins': user_stats.admins or 0
        },
        'tags': {
            'total': Tag.query.count()
        }
    })
