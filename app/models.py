from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

# Association table for many-to-many relationship between characters and tags
character_tags = db.Table('character_tags',
    db.Column('character_id', db.Integer, db.<PERSON>('character.id'), primary_key=True),
    db.Column('tag_id', db.Integer, db.<PERSON>ey('tag.id'), primary_key=True)
)

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Character(db.Model):
    id = db.Column(db.Integer, primary_key=True)

    # Basic Information
    character_id = db.Column(db.String(100), unique=True)  # Unique identifier like "ayumi"
    name = db.Column(db.String(100), nullable=False)
    age = db.Column(db.Integer)
    occupation = db.Column(db.String(200))
    country = db.Column(db.String(200))
    hobbies = db.Column(db.String(500))

    # Physical Appearance
    hair_color = db.Column(db.String(100))
    hair_style = db.Column(db.String(100))
    eye_color = db.Column(db.String(100))
    eye_type = db.Column(db.String(200))
    face_detail = db.Column(db.Text)
    body_detail = db.Column(db.Text)
    skin_color = db.Column(db.String(100))

    # Personality & Roleplay
    personality_roleplay = db.Column(db.Text)
    style_roleplay = db.Column(db.Text)
    nsfw_style = db.Column(db.Text)

    # System Information
    timezone = db.Column(db.Integer, default=0)
    gender = db.Column(db.String(50))
    types = db.Column(db.String(100))  # e.g., "Anime", "Realistic"

    # AI Generation Prompts
    prompt_gen_image = db.Column(db.Text)
    prompt_negative_gen_image = db.Column(db.Text)
    prompt_gen_scenario_image = db.Column(db.Text)

    # Additional Information
    bio = db.Column(db.Text)
    scenario_information = db.Column(db.Text)
    voice_id = db.Column(db.String(100))
    match_rate_roleset = db.Column(db.Text)  # JSON array as string

    # Legacy fields (for backward compatibility)
    description = db.Column(db.Text)
    attributes = db.Column(db.Text)  # JSON string for flexible attributes
    template_image = db.Column(db.String(255))  # Path to template image
    thumbnail = db.Column(db.String(255))  # Path to thumbnail

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # Relationships
    creator = db.relationship('User', backref='characters')
    generated_images = db.relationship('GeneratedImage', backref='character', lazy='dynamic', cascade='all, delete-orphan')
    tags = db.relationship('Tag', secondary=character_tags, backref='characters')

    def __repr__(self):
        return f'<Character {self.name}>'

    def get_match_rate_roleset_list(self):
        """Convert match_rate_roleset JSON string to list"""
        if self.match_rate_roleset:
            try:
                import json
                return json.loads(self.match_rate_roleset)
            except:
                return []
        return []

    def set_match_rate_roleset_list(self, roleset_list):
        """Convert list to JSON string for storage"""
        import json
        self.match_rate_roleset = json.dumps(roleset_list) if roleset_list else None

class GeneratedImage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255))
    thumbnail = db.Column(db.String(255))
    file_size = db.Column(db.Integer)
    width = db.Column(db.Integer)
    height = db.Column(db.Integer)
    generation_parameters = db.Column(db.Text)  # JSON string for generation metadata
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    character_id = db.Column(db.Integer, db.ForeignKey('character.id'), nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Relationships
    uploader = db.relationship('User', backref='uploaded_images')
    
    def __repr__(self):
        return f'<GeneratedImage {self.filename}>'

class Tag(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    color = db.Column(db.String(7), default='#007bff')  # Hex color for tag display
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Tag {self.name}>'
