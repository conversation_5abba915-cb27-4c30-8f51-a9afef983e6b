from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
from config import config

db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
csrf = CSRFProtect()

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)

    # Initialize MongoDB service
    from app.services import mongodb_service, qdrant_service
    mongodb_service.init_app(app)

    # Initialize Qdrant service
    qdrant_service.init_app(app)
    
    # Configure login manager with enhanced settings
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    login_manager.session_protection = 'strong'  # Enhanced session protection
    login_manager.refresh_view = 'auth.login'
    login_manager.needs_refresh_message = 'Please re-authenticate to access this page.'
    login_manager.needs_refresh_message_category = 'info'
    
    # Register blueprints
    from app.routes.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    from app.routes.auth import auth as auth_blueprint
    app.register_blueprint(auth_blueprint, url_prefix='/auth')
    
    from app.routes.characters import characters as characters_blueprint
    app.register_blueprint(characters_blueprint, url_prefix='/characters')
    
    from app.routes.images import images as images_blueprint
    app.register_blueprint(images_blueprint, url_prefix='/images')
    
    from app.routes.api import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix='/api')

    from app.routes.admin import admin as admin_blueprint
    app.register_blueprint(admin_blueprint, url_prefix='/admin')

    from app.routes.mongodb_api import mongodb_api as mongodb_api_blueprint
    app.register_blueprint(mongodb_api_blueprint, url_prefix='/api/mongodb')

    # Disable CSRF for API routes
    csrf.exempt(api_blueprint)
    csrf.exempt(mongodb_api_blueprint)

    # Add context processors for authentication utilities
    @app.context_processor
    def inject_auth_utils():
        from app.utils.auth_utils import get_user_permissions, can_edit_character, can_delete_character, can_edit_image, can_delete_image
        return {
            'user_permissions': get_user_permissions(),
            'can_edit_character': can_edit_character,
            'can_delete_character': can_delete_character,
            'can_edit_image': can_edit_image,
            'can_delete_image': can_delete_image
        }

    return app
